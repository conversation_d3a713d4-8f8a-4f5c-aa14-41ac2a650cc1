/*
 * Copyright(C) 2024 Spes Co., Ltd. All rights reserved.
 */

package com.spes.sop.third.kingdee.service;

import com.spes.sop.third.kingdee.config.KingdeeConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 金蝶多账套服务测试类
 *
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest(classes = {KingdeeConfig.class, KingdeeAccountService.class})
@EnableConfigurationProperties(KingdeeConfig.class)
@TestPropertySource(properties = {
        "kingdee.serverUrl=http://192.168.50.56/k3cloud/",
        "kingdee.acctId=66d67292cd0454",
        "kingdee.username=administrator",
        "kingdee.password=kingdee123@",
        "kingdee.lcid=2052",
        "kingdee.connectTimeout=10000",
        "kingdee.readTimeout=30000",
        "kingdee.sessionExpireTime=3600000",
        "kingdee.accounts.默认=66d67292cd0454",
        "kingdee.accounts.品牌A=66d67292cd0454",
        "kingdee.accounts.品牌B=66d67292cd0455"
})
@DisplayName("金蝶多账套服务测试")
class KingdeeMultiAccountServiceTest {

    @Autowired
    private KingdeeConfig kingdeeConfig;

    @Autowired
    private KingdeeAccountService kingdeeAccountService;

    @Test
    @DisplayName("测试多账套配置加载")
    void testMultiAccountConfigLoading() {
        log.info("开始测试多账套配置加载...");

        // 验证配置对象不为空
        assertNotNull(kingdeeConfig, "金蝶配置对象应该不为空");
        assertNotNull(kingdeeConfig.getAccounts(), "多账套配置应该不为空");

        // 验证多账套配置
        assertEquals(3, kingdeeConfig.getAccounts().size(), "应该有3个账套配置");
        assertTrue(kingdeeConfig.getAccounts().containsKey("默认"), "应该包含默认账套");
        assertTrue(kingdeeConfig.getAccounts().containsKey("品牌A"), "应该包含品牌A账套");
        assertTrue(kingdeeConfig.getAccounts().containsKey("品牌B"), "应该包含品牌B账套");

        log.info("✅ 多账套配置加载验证成功:");
        log.info("  账套配置: {}", kingdeeConfig.getAccounts());
    }

    @Test
    @DisplayName("测试根据品牌获取账套ID")
    void testGetAcctIdByBrand() {
        log.info("开始测试根据品牌获取账套ID...");

        // 测试已配置的品牌
        String defaultAcctId = kingdeeConfig.getAcctIdByBrand("默认");
        assertEquals("66d67292cd0454", defaultAcctId, "默认账套ID应该匹配");

        String brandAAcctId = kingdeeConfig.getAcctIdByBrand("品牌A");
        assertEquals("66d67292cd0454", brandAAcctId, "品牌A账套ID应该匹配");

        String brandBAcctId = kingdeeConfig.getAcctIdByBrand("品牌B");
        assertEquals("66d67292cd0455", brandBAcctId, "品牌B账套ID应该匹配");

        // 测试未配置的品牌（应该返回默认账套）
        String unknownBrandAcctId = kingdeeConfig.getAcctIdByBrand("未知品牌");
        assertEquals("66d67292cd0454", unknownBrandAcctId, "未知品牌应该返回默认账套ID");

        // 测试空品牌名称
        String emptyBrandAcctId = kingdeeConfig.getAcctIdByBrand("");
        assertEquals("66d67292cd0454", emptyBrandAcctId, "空品牌名称应该返回默认账套ID");

        log.info("✅ 根据品牌获取账套ID验证成功:");
        log.info("  默认账套ID: {}", defaultAcctId);
        log.info("  品牌A账套ID: {}", brandAAcctId);
        log.info("  品牌B账套ID: {}", brandBAcctId);
        log.info("  未知品牌账套ID: {}", unknownBrandAcctId);
    }

    @Test
    @DisplayName("测试账套服务获取账套ID")
    void testAccountServiceGetAcctId() {
        log.info("开始测试账套服务获取账套ID...");

        // 测试通过服务获取账套ID
        String defaultAcctId = kingdeeAccountService.getAcctIdByBrand("默认");
        assertEquals("66d67292cd0454", defaultAcctId, "默认账套ID应该匹配");

        String brandAAcctId = kingdeeAccountService.getAcctIdByBrand("品牌A");
        assertEquals("66d67292cd0454", brandAAcctId, "品牌A账套ID应该匹配");

        String brandBAcctId = kingdeeAccountService.getAcctIdByBrand("品牌B");
        assertEquals("66d67292cd0455", brandBAcctId, "品牌B账套ID应该匹配");

        log.info("✅ 账套服务获取账套ID验证成功:");
        log.info("  默认账套ID: {}", defaultAcctId);
        log.info("  品牌A账套ID: {}", brandAAcctId);
        log.info("  品牌B账套ID: {}", brandBAcctId);
    }

    @Test
    @DisplayName("测试获取K3CloudTemplate")
    void testGetTemplate() {
        log.info("开始测试获取K3CloudTemplate...");

        // 测试获取不同品牌的模板
        assertNotNull(kingdeeAccountService.getTemplateByBrand("默认"), "默认品牌模板应该不为空");
        assertNotNull(kingdeeAccountService.getTemplateByBrand("品牌A"), "品牌A模板应该不为空");
        assertNotNull(kingdeeAccountService.getTemplateByBrand("品牌B"), "品牌B模板应该不为空");

        // 测试获取默认模板
        assertNotNull(kingdeeAccountService.getDefaultTemplate(), "默认模板应该不为空");

        log.info("✅ 获取K3CloudTemplate验证成功");
    }

    @Test
    @DisplayName("测试模板缓存")
    void testTemplateCache() {
        log.info("开始测试模板缓存...");

        // 获取相同品牌的模板两次，应该是同一个实例
        Object template1 = kingdeeAccountService.getTemplateByBrand("品牌A");
        Object template2 = kingdeeAccountService.getTemplateByBrand("品牌A");
        assertSame(template1, template2, "相同品牌的模板应该是同一个实例");

        // 获取不同品牌的模板，应该是不同的实例
        Object templateA = kingdeeAccountService.getTemplateByBrand("品牌A");
        Object templateB = kingdeeAccountService.getTemplateByBrand("品牌B");
        assertNotSame(templateA, templateB, "不同品牌的模板应该是不同的实例");

        log.info("✅ 模板缓存验证成功");
    }

    @Test
    @DisplayName("测试清除模板缓存")
    void testClearTemplateCache() {
        log.info("开始测试清除模板缓存...");

        // 获取模板
        Object template1 = kingdeeAccountService.getTemplateByBrand("品牌A");
        assertNotNull(template1, "模板应该不为空");

        // 清除缓存
        kingdeeAccountService.clearTemplateCache();

        // 再次获取模板，应该是新的实例
        Object template2 = kingdeeAccountService.getTemplateByBrand("品牌A");
        assertNotNull(template2, "模板应该不为空");
        assertNotSame(template1, template2, "清除缓存后应该是新的模板实例");

        log.info("✅ 清除模板缓存验证成功");
    }
} 