<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.spes.sop.goods.mapper.SkuMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.spes.sop.goods.mapper.model.entity.SkuDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="spu_id" property="spuId" jdbcType="BIGINT"/>
        <result column="sku_code" property="skuCode" jdbcType="VARCHAR"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="specification" property="specification" jdbcType="INTEGER"/>
        <result column="bar_code" property="barCode" jdbcType="VARCHAR"/>
        <result column="filing_number" property="filingNumber" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="oa_id" property="oaId" jdbcType="BIGINT"/>
        <result column="sync_fail" property="syncFail" jdbcType="VARCHAR"/>
        <result column="gift" property="gift" jdbcType="TINYINT"/>
        <result column="card" property="card" jdbcType="TINYINT"/>
        <result column="business_info" property="businessInfo" jdbcType="LONGVARCHAR"/>
        <result column="product_info" property="productInfo" jdbcType="LONGVARCHAR"/>
        <result column="supply_chain_info" property="supplyChainInfo" jdbcType="LONGVARCHAR"/>
        <result column="fair_price" property="fairPrice" jdbcType="DECIMAL"/>
        <result column="base_price" property="basePrice" jdbcType="DECIMAL"/>
        <result column="cost" property="cost" jdbcType="DECIMAL"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator_id" property="creatorId" jdbcType="BIGINT"/>
        <result column="updater_id" property="updaterId" jdbcType="BIGINT"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id
        , spu_id, sku_code, sku_name, specification, bar_code, filing_number, status, oa_id, sync_fail,
        gift, card, business_info, product_info, supply_chain_info,
        fair_price, base_price, cost, create_time, update_time, creator_id, updater_id, deleted
    </sql>

    <!-- 列表查询通用条件 -->
    <sql id="List_Where_Clause">
        <where>
            <if test="query.ids != null and query.ids.size() > 0">
                AND id IN
                <foreach collection="query.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="query.spuIds != null and query.spuIds.size() > 0">
                AND spu_id IN
                <foreach collection="query.spuIds" item="spuId" open="(" separator="," close=")">
                    #{spuId}
                </foreach>
            </if>
            <if test="query.skuCodes != null and query.skuCodes.size() > 0">
                AND sku_code IN
                <foreach collection="query.skuCodes" item="skuCode" open="(" separator="," close=")">
                    #{skuCode}
                </foreach>
            </if>
            <if test="query.skuNameLike != null and query.skuNameLike != ''">
                AND sku_name LIKE CONCAT('%', #{query.skuNameLike}, '%')
            </if>
            <if test="query.filingNumbers != null and query.filingNumbers.size() > 0">
                AND filing_number IN
                <foreach collection="query.filingNumbers" item="filingNumber" open="(" separator="," close=")">
                    #{filingNumber}
                </foreach>
            </if>
            <if test="query.barCodes != null and query.barCodes.size() > 0">
                AND bar_code IN
                <foreach collection="query.barCodes" item="barCode" open="(" separator="," close=")">
                    #{barCode}
                </foreach>
            </if>
            <if test="query.statuses != null and query.statuses.size() > 0">
                AND status IN
                <foreach collection="query.statuses" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="query.gift != null">
                AND gift = #{query.gift}
            </if>
            <if test="query.card != null">
                AND card = #{query.card}
            </if>
            <if test="true">
                AND deleted = 0
            </if>
        </where>
    </sql>

    <!-- 单个查询通用条件 -->
    <sql id="Get_Where_Clause">
        <where>
            <if test="query.id != null">
                AND id = #{query.id}
            </if>
            <if test="query.skuCode != null and query.skuCode != ''">
                AND sku_code = #{query.skuCode}
            </if>
            <if test="query.barCode != null and query.barCode != ''">
                AND bar_code = #{query.barCode}
            </if>
            <if test="query.filingNumber != null and query.filingNumber != ''">
                AND filing_number = #{query.filingNumber}
            </if>
            <if test="query.deleted != null">
                AND deleted = #{query.deleted}
            </if>
        </where>
    </sql>

    <!-- 通用排序 -->
    <sql id="Common_Order_By">
        <choose>
            <when test="query.orderBy != null and query.orderBy != ''">
                ORDER BY ${query.orderBy}
                <if test="query.orderDirection != null and query.orderDirection != ''">
                    ${query.orderDirection}
                </if>
            </when>
            <otherwise>
                ORDER BY create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 分页查询SKU列表 -->
    <select id="list" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sop_sku_info
        <include refid="List_Where_Clause"/>
        <include refid="Common_Order_By"/>
        <if test="query.limit != null and query.limit > 0">
            LIMIT #{query.offset}, #{query.limit}
        </if>
    </select>

    <!-- 查询SKU总数 -->
    <select id="count" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM sop_sku_info
        <include refid="List_Where_Clause"/>
    </select>

    <!-- 查询单个SKU -->
    <select id="getSku" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sop_sku_info
        <include refid="Get_Where_Clause"/>
        ORDER BY create_time DESC
        LIMIT 1
    </select>
    <!-- 新增单个SKU -->
    <insert id="insert" parameterType="com.spes.sop.goods.mapper.model.req.SkuAddReq" useGeneratedKeys="true"
            keyProperty="req.id">
        INSERT INTO sop_sku_info (spu_id,
                                  sku_code,
                                  sku_name,
                                  bar_code,
                                  specification,
                                  filing_number,
                                  status,
                                  oa_id,
                                  gift,
                                  card,
                                  business_info,
                                  product_info,
                                  supply_chain_info,
                                  fair_price,
                                  base_price,
                                  cost,
                                  creator_id,
                                  updater_id,
                                  deleted,
                                  create_time,
                                  update_time)
        VALUES (#{req.spuId},
                #{req.skuCode},
                #{req.skuName},
                #{req.barCode},
                #{req.specification},
                #{req.filingNumber},
                #{req.status},
                #{req.oaId},
                COALESCE(#{req.gift}, 0),
                COALESCE(#{req.card}, 0),
                #{req.businessInfo},
                #{req.productInfo},
                #{req.supplyChainInfo},
                #{req.fairPrice},
                #{req.basePrice},
                #{req.cost},
                #{req.creatorId},
                #{req.updaterId},
                0,
                NOW(),
                NOW())
    </insert>

    <!-- 批量新增SKU -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO sop_sku_info (
        spu_id,
        sku_code,
        sku_name,
        bar_code,
        specification,
        filing_number,
        status,
        oa_id,
        gift,
        card,
        business_info,
        product_info,
        supply_chain_info,
        fair_price,
        base_price,
        cost,
        creator_id,
        updater_id,
        deleted,
        create_time,
        update_time
        ) VALUES
        <foreach collection="skus" item="req" separator=",">
            (
            #{req.spuId},
            #{req.skuCode},
            #{req.skuName},
            #{req.barCode},
            #{req.specification},
            #{req.filingNumber},
            #{req.status},
            #{req.oaId},
            COALESCE(#{req.gift}, 0),
            COALESCE(#{req.card}, 0),
            #{req.businessInfo},
            #{req.productInfo},
            #{req.supplyChainInfo},
            #{req.fairPrice},
            #{req.basePrice},
            #{req.cost},
            #{req.creatorId},
            #{req.updaterId},
            0,
            NOW(),
            NOW()
            )
        </foreach>
    </insert>

    <!-- 根据ID更新SKU -->
    <update id="updateById" parameterType="com.spes.sop.goods.mapper.model.req.SkuUpdateReq">
        UPDATE sop_sku_info
        <set>
            <if test="req.spuId != null">
                spu_id = #{req.spuId},
            </if>
            <if test="req.skuCode != null">
                sku_code = #{req.skuCode},
            </if>
            <if test="req.skuName != null">
                sku_name = #{req.skuName},
            </if>
            <if test="req.barCode != null">
                bar_code = #{req.barCode},
            </if>
            <if test="req.filingNumber != null">
                filing_number = #{req.filingNumber},
            </if>
            <if test="req.status != null">
                status = #{req.status},
            </if>
            <if test="req.oaId != null">
                oa_id = #{req.oaId},
            </if>
            <if test="req.syncFail != null">
                sync_fail = #{req.syncFail},
            </if>
            <if test="req.gift != null">
                gift = #{req.gift},
            </if>
            <if test="req.card != null">
                card = #{req.card},
            </if>
            <if test="req.businessInfo != null">
                business_info = #{req.businessInfo},
            </if>
            <if test="req.productInfo != null">
                product_info = #{req.productInfo},
            </if>
            <if test="req.supplyChainInfo != null">
                supply_chain_info = #{req.supplyChainInfo},
            </if>
            <if test="req.fairPrice != null">
                fair_price = #{req.fairPrice},
            </if>
            <if test="req.basePrice != null">
                base_price = #{req.basePrice},
            </if>
            <if test="req.cost != null">
                cost = #{req.cost},
            </if>
            <if test="req.updaterId != null">
                updater_id = #{req.updaterId},
            </if>
            <if test="req.deleted != null">
                deleted = #{req.deleted},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{req.id}
    </update>

    <!-- 根据ID逻辑删除SKU -->
    <update id="deleteById">
        UPDATE sop_sku_info
        SET deleted     = 1,
            updater_id  = #{updaterId},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据ID批量逻辑删除SKU -->
    <update id="deleteBatch">
        UPDATE sop_sku_info
        SET deleted = 1,
        updater_id = #{updaterId},
        update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据年月统计SKU数量 -->
    <select id="countByYearMonth" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM sop_sku_info
        WHERE sku_code like #{codePrefix}
          AND deleted = 0
    </select>

</mapper> 