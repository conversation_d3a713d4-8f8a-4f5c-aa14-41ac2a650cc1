package com.spes.sop.goods.mapper.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 商品系列实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsSeriesDO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 系列名称
     */
    private String name;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Boolean deleted;
} 
