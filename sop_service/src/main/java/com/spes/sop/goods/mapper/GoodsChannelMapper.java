package com.spes.sop.goods.mapper;

import com.spes.sop.goods.mapper.model.entity.GoodsChannelDO;
import com.spes.sop.goods.mapper.model.query.GoodsChannelDOGetQuery;
import com.spes.sop.goods.mapper.model.query.GoodsChannelDOListQuery;
import com.spes.sop.goods.mapper.model.req.GoodsChannelAddReq;
import com.spes.sop.goods.mapper.model.req.GoodsChannelDeleteReq;
import com.spes.sop.goods.mapper.model.req.GoodsChannelUpdateReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品渠道Mapper接口
 */
@Mapper
public interface GoodsChannelMapper {

    /**
     * 分页查询商品渠道列表
     *
     * @param query 查询条件
     * @return 商品渠道列表
     */
    List<GoodsChannelDO> list(@Param("query") GoodsChannelDOListQuery query);

    /**
     * 查询商品渠道总数
     *
     * @param query 查询条件
     * @return 总数
     */
    Long count(@Param("query") GoodsChannelDOListQuery query);

    /**
     * 查询单个商品渠道
     *
     * @param query 查询条件
     * @return 商品渠道信息
     */
    GoodsChannelDO getGoodsChannel(@Param("query") GoodsChannelDOGetQuery query);

    /**
     * 新增商品渠道
     *
     * @param req 新增请求
     * @return 影响行数
     */
    int insert(@Param("req") GoodsChannelAddReq req);

    /**
     * 批量新增商品渠道
     *
     * @param reqList 新增请求列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<GoodsChannelAddReq> reqList);

    /**
     * 更新商品渠道
     *
     * @param req 更新请求
     * @return 影响行数
     */
    int updateById(@Param("req") GoodsChannelUpdateReq req);

    /**
     * 逻辑删除商品渠道
     *
     * @param req 删除请求
     * @return 影响行数
     */
    int delete(@Param("req") GoodsChannelDeleteReq req);
} 
