package com.spes.sop.goods.service.common.model.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 商品通用树形业务对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsCommonTreeBO {

    /**
     * 分类ID
     */
    private Long id;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 子分类列表
     */
    private List<GoodsCommonTreeBO> children;
}
