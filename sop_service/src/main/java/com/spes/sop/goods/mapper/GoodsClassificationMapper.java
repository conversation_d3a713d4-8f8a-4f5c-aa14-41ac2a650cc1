package com.spes.sop.goods.mapper;

import com.spes.sop.goods.mapper.model.entity.GoodsClassificationDO;
import com.spes.sop.goods.mapper.model.query.GoodsClassificationDOGetQuery;
import com.spes.sop.goods.mapper.model.query.GoodsClassificationDOListQuery;
import com.spes.sop.goods.mapper.model.req.GoodsClassificationAddReq;
import com.spes.sop.goods.mapper.model.req.GoodsClassificationDeleteReq;
import com.spes.sop.goods.mapper.model.req.GoodsClassificationUpdateReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品分类Mapper接口
 */
@Mapper
public interface GoodsClassificationMapper {

    /**
     * 分页查询商品分类列表
     *
     * @param query 查询条件
     * @return 商品分类列表
     */
    List<GoodsClassificationDO> list(@Param("query") GoodsClassificationDOListQuery query);

    /**
     * 查询商品分类总数
     *
     * @param query 查询条件
     * @return 总数
     */
    Long count(@Param("query") GoodsClassificationDOListQuery query);

    /**
     * 查询单个商品分类
     *
     * @param query 查询条件
     * @return 商品分类信息
     */
    GoodsClassificationDO getGoodsClassification(@Param("query") GoodsClassificationDOGetQuery query);

    /**
     * 新增商品分类
     *
     * @param req 新增请求
     * @return 影响行数
     */
    int insert(@Param("req") GoodsClassificationAddReq req);

    /**
     * 批量新增商品分类
     *
     * @param reqList 新增请求列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<GoodsClassificationAddReq> reqList);

    /**
     * 更新商品分类
     *
     * @param req 更新请求
     * @return 影响行数
     */
    int updateById(@Param("req") GoodsClassificationUpdateReq req);

    /**
     * 逻辑删除商品分类
     *
     * @param req 删除请求
     * @return 影响行数
     */
    int delete(@Param("req") GoodsClassificationDeleteReq req);
} 
