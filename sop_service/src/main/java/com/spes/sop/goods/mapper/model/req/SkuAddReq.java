package com.spes.sop.goods.mapper.model.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * SKU新增请求
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuAddReq {

    /**
     * SKU主键ID（数据库自动生成）
     */
    private Long id;

    /**
     * 货品ID（SPU ID）
     */
    private String spuId;
    /**
     * 规格
     */
    private Long specification;

    /**
     * 货品编码
     */
    private String skuCode;

    /**
     * 下单产品名称
     */
    private String skuName;

    /**
     * 条码
     */
    private String barCode;

    /**
     * 备案号
     */
    private String filingNumber;

    /**
     * 状态
     */
    private String status;

    /**
     * 审批流程 id
     */
    private Long oaId;

    /**
     * 是否赠品（1-是，0-否）
     */
    private Integer gift;

    /**
     * 是否为包裹卡（1-是，0-否）
     */
    private Integer card;

    /**
     * 运营信息（JSON格式存储）
     */
    private String businessInfo;

    /**
     * 产品信息（JSON格式存储）
     */
    private String productInfo;

    /**
     * 供应链信息（JSON格式存储）
     */
    private String supplyChainInfo;

    /**
     * 公允价值
     */
    private BigDecimal fairPrice;

    /**
     * 底价
     */
    private BigDecimal basePrice;

    /**
     * 成本
     */
    private BigDecimal cost;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 更新人ID
     */
    private Long updaterId;
} 