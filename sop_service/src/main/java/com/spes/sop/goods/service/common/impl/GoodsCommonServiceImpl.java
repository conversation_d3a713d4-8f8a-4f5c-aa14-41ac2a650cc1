package com.spes.sop.goods.service.common.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.spes.sop.goods.mapper.GoodsCategoryMapper;
import com.spes.sop.goods.mapper.GoodsChannelMapper;
import com.spes.sop.goods.mapper.GoodsClassificationMapper;
import com.spes.sop.goods.mapper.GoodsSeriesMapper;
import com.spes.sop.goods.mapper.model.entity.GoodsCategoryDO;
import com.spes.sop.goods.mapper.model.entity.GoodsChannelDO;
import com.spes.sop.goods.mapper.model.entity.GoodsClassificationDO;
import com.spes.sop.goods.mapper.model.entity.GoodsSeriesDO;
import com.spes.sop.goods.mapper.model.query.GoodsCategoryDOListQuery;
import com.spes.sop.goods.mapper.model.query.GoodsChannelDOListQuery;
import com.spes.sop.goods.mapper.model.query.GoodsClassificationDOListQuery;
import com.spes.sop.goods.mapper.model.query.GoodsSeriesDOListQuery;
import com.spes.sop.goods.service.common.GoodsCommonService;
import com.spes.sop.goods.service.common.convert.GoodsCommonConvert;
import com.spes.sop.goods.service.common.model.bo.GoodsCommonBO;
import com.spes.sop.goods.service.common.model.bo.GoodsCommonTreeBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class GoodsCommonServiceImpl implements GoodsCommonService {

    private final GoodsChannelMapper goodsChannelMapper;
    private final GoodsSeriesMapper goodsSeriesMapper;
    private final GoodsCategoryMapper goodsCategoryMapper;
    private final GoodsClassificationMapper goodsClassificationMapper;


    @Override
    public List<GoodsCommonBO> getChannels() {
        List<GoodsChannelDO> result = goodsChannelMapper.list(GoodsChannelDOListQuery.builder().build());
        return GoodsCommonConvert.channelConvert(result);
    }

    @Override
    public List<GoodsCommonBO> getSeries() {
        List<GoodsSeriesDO> result = goodsSeriesMapper.list(GoodsSeriesDOListQuery.builder().build());
        return GoodsCommonConvert.seriesConvert(result);
    }

    @Override
    public List<GoodsCommonBO> getCategory() {
        List<GoodsCategoryDO> result =
                goodsCategoryMapper.list(GoodsCategoryDOListQuery.builder().build());
        return GoodsCommonConvert.categoryConvert(result);
    }

    @Override
    public List<GoodsCommonBO> getChannels(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        List<GoodsChannelDO> result = goodsChannelMapper.list(GoodsChannelDOListQuery.builder()
                .ids(ids).build());
        return GoodsCommonConvert.channelConvert(result);
    }

    @Override
    public List<GoodsCommonBO> getSeries(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        List<GoodsSeriesDO> result = goodsSeriesMapper.list(GoodsSeriesDOListQuery.builder()
                .ids(ids).build());
        return GoodsCommonConvert.seriesConvert(result);
    }

    @Override
    public List<GoodsCommonBO> getCategory(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        List<GoodsCategoryDO> result =
                goodsCategoryMapper.list(GoodsCategoryDOListQuery.builder()
                        .ids(ids).build());
        return GoodsCommonConvert.categoryConvert(result);
    }

    @Override
    public List<GoodsCommonBO> getClassification(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        List<GoodsClassificationDO> classifications =
                goodsClassificationMapper.list(GoodsClassificationDOListQuery.builder()
                        .ids(ids).build());
        return GoodsCommonConvert.classificationConvert(classifications);
    }

    @Override
    public List<GoodsCommonBO> getChannelsByName(List<String> names) {
        if (CollUtil.isEmpty(names)) {
            return Lists.newArrayList();
        }
        List<GoodsChannelDO> channels = goodsChannelMapper.list(GoodsChannelDOListQuery.builder()
                .names(names).build());
        return GoodsCommonConvert.channelConvert(channels);
    }

    @Override
    public List<GoodsCommonBO> getSeriesByName(List<String> names) {
        if (CollUtil.isEmpty(names)) {
            return Lists.newArrayList();
        }
        List<GoodsSeriesDO> series = goodsSeriesMapper.list(GoodsSeriesDOListQuery.builder()
                .names(names).build());
        return GoodsCommonConvert.seriesConvert(series);
    }

    @Override
    public List<GoodsCommonBO> getCategoryByName(List<String> names) {
        if (CollUtil.isEmpty(names)) {
            return Lists.newArrayList();
        }
        List<GoodsCategoryDO> categories = goodsCategoryMapper.list(GoodsCategoryDOListQuery.builder()
                .names(names).build());
        return GoodsCommonConvert.categoryConvert(categories);
    }

    @Override
    public List<GoodsCommonBO> getClassificationByName(List<String> names) {
        if (CollUtil.isEmpty(names)) {
            return Lists.newArrayList();
        }
        List<GoodsClassificationDO> classifications =
                goodsClassificationMapper.list(GoodsClassificationDOListQuery.builder()
                        .names(names).build());
        return GoodsCommonConvert.classificationConvert(classifications);
    }

    @Override
    public List<GoodsCommonTreeBO> getClassificationTree() {
        List<GoodsClassificationDO> classifications =
                goodsClassificationMapper.list(GoodsClassificationDOListQuery.builder().build());
        //列表转成树
        if (CollUtil.isEmpty(classifications)) {
            return Lists.newArrayList();
        }

        // 将列表根据 parentId 转换为多层级的树形结构
        Map<Long, List<GoodsClassificationDO>> parentMap =
                classifications.stream()
                        .collect(Collectors.groupingBy(GoodsClassificationDO::getParentId));

        // 递归构建树形结构
        return buildTree(0L, parentMap);
    }

    private List<GoodsCommonTreeBO> buildTree(long parentId, Map<Long, List<GoodsClassificationDO>> parentMap) {
        //结束条件
        if (!parentMap.containsKey(parentId)) {
            return null;
        }
        // 递归构建树形结构
        return parentMap.getOrDefault(parentId, Collections.emptyList()).stream()
                .map(classification -> GoodsCommonTreeBO.builder()
                        .id(classification.getId())
                        .name(classification.getName())
                        .children(buildTree(classification.getId(), parentMap))
                        .build())
                .collect(Collectors.toList());
    }
}
