package com.spes.sop.goods.mapper.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 分类实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClassificationDO {

    /**
     * 分类ID
     */
    private Long id;

    /**
     * 类目名称
     */
    private String name;

    /**
     * 父类目ID
     */
    private Long parentId;

    /**
     * 类目层级
     */
    private Integer classLevel;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Boolean deleted;
} 
