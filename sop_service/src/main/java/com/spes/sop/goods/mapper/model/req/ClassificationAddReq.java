package com.spes.sop.goods.mapper.model.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分类新增请求对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClassificationAddReq {

    /**
     * 类目名称
     */
    private String name;

    /**
     * 父类目ID
     */
    private Long parentId;

    /**
     * 类目层级
     */
    private Integer classLevel;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 更新人ID
     */
    private Long updaterId;
} 
