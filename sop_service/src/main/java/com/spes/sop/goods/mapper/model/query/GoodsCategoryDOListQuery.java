package com.spes.sop.goods.mapper.model.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 商品分类列表查询对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsCategoryDOListQuery {

    /**
     * 主键ID
     */
    private List<Long> ids;
    /**
     * 产品分类名称
     */
    private List<String> names;

    /**
     * 分页起始位置
     */
    private Integer offset;

    /**
     * 分页大小
     */
    private Integer limit;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向
     */
    private String orderDirection;
} 
