package com.spes.sop.goods.service.common;

import com.spes.sop.goods.service.common.model.bo.GoodsCommonBO;
import com.spes.sop.goods.service.common.model.bo.GoodsCommonTreeBO;

import java.util.List;

public interface GoodsCommonService {

    /**
     * 获取全部渠道信息
     */
    List<GoodsCommonBO> getChannels();

    /**
     * 获取全部系列信息
     */
    List<GoodsCommonBO> getSeries();

    /**
     * 获取全部分类信息
     */
    List<GoodsCommonBO> getCategory();

    /**
     * 获取全部商品分类树形结构
     */
    List<GoodsCommonTreeBO> getClassificationTree();

    /**
     * 获取渠道信息
     */
    List<GoodsCommonBO> getChannels(List<Long> ids);

    /**
     * 获取系列信息
     */
    List<GoodsCommonBO> getSeries(List<Long> ids);

    /**
     * 获取分类信息
     */
    List<GoodsCommonBO> getCategory(List<Long> ids);

    /**
     * 获取商品分类信息
     */
    List<GoodsCommonBO> getClassification(List<Long> ids);

    /**
     * 获取渠道信息
     */
    List<GoodsCommonBO> getChannelsByName(List<String> names);

    /**
     * 获取系列信息
     */
    List<GoodsCommonBO> getSeriesByName(List<String> ids);

    /**
     * 获取分类信息
     */
    List<GoodsCommonBO> getCategoryByName(List<String> ids);

    /**
     * 获取商品分类信息
     */
    List<GoodsCommonBO> getClassificationByName(List<String> ids);



}
