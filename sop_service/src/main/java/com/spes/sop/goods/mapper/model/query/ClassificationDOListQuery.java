package com.spes.sop.goods.mapper.model.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分类列表查询对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClassificationDOListQuery {

    /**
     * 分类ID
     */
    private Long id;

    /**
     * 类目名称
     */
    private String name;

    /**
     * 父类目ID
     */
    private Long parentId;

    /**
     * 类目层级
     */
    private Integer classLevel;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 分页起始位置
     */
    private Integer offset;

    /**
     * 分页大小
     */
    private Integer limit;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向
     */
    private String orderDirection;
} 
