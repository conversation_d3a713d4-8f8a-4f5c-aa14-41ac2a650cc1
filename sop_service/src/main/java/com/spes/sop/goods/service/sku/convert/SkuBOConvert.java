package com.spes.sop.goods.service.sku.convert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.spes.sop.common.enums.GoodsStatusEnum;
import com.spes.sop.common.util.JsonUtils;
import com.spes.sop.goods.mapper.model.entity.SkuDO;
import com.spes.sop.goods.mapper.model.query.SkuDOListQuery;
import com.spes.sop.goods.mapper.model.req.SkuAddReq;
import com.spes.sop.goods.mapper.model.req.SkuUpdateReq;
import com.spes.sop.goods.service.sku.model.bo.SkuDetailBO;
import com.spes.sop.goods.service.sku.model.bo.SkuListBO;
import com.spes.sop.goods.service.sku.model.bo.value.SkuBusinessInfoValueBO;
import com.spes.sop.goods.service.sku.model.bo.value.SkuProductInfoValueBO;
import com.spes.sop.goods.service.sku.model.bo.value.SkuSupplyChainInfoValueBO;
import com.spes.sop.goods.service.sku.model.command.SkuCreateCommand;
import com.spes.sop.goods.service.sku.model.command.SkuUpdateCommand;
import com.spes.sop.goods.service.sku.model.query.SkuBOListQuery;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SKU业务对象转换器
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public class SkuBOConvert {

    public static SkuDOListQuery convert(SkuBOListQuery query) {
        SkuDOListQuery result = SkuDOListQuery.builder()
                .ids(query.getIds())
                .skuNameLike(query.getSkuNameSearch())
                .skuCodes(query.getSkuCodes())
                .spuIds(query.getSpuIds())
                .barCodes(query.getBarCodes())
                .filingNumbers(query.getFilingNumbers())
                .statuses(CollUtil.isEmpty(query.getStatuses()) ? null :
                        query.getStatuses().stream().map(GoodsStatusEnum::name).collect(Collectors.toList()))
                .series(query.getSeries())
                .channelIds(query.getChannelIds())
                .specifications(query.getSpecifications())
                .gift(query.getGift())
                .card(query.getCard())
                .build();
        if (ObjectUtil.isNotNull(query.getPager())) {
            result.setOffset(query.getPager().getOffset());
            result.setLimit(query.getPager().getLimit());
        }
        return result;
    }

    public static List<SkuListBO> convert(List<SkuDO> skus) {
        if (CollUtil.isEmpty(skus)) {
            return Lists.newArrayList();
        }
        return skus.stream()
                .map(SkuBOConvert::convert)
                .collect(Collectors.toList());
    }

    /**
     * 将SkuDO转换为SkuDetailBO
     *
     * @param skuDO SKU数据对象
     * @return SKU详情业务对象
     */
    public static SkuDetailBO convertToDetailBO(SkuDO skuDO) {
        if (skuDO == null) {
            return null;
        }

        return SkuDetailBO.builder()
                .id(skuDO.getId())
                .spuId(skuDO.getSpuId())
                .skuCode(skuDO.getSkuCode())
                .skuName(skuDO.getSkuName())
                .barCode(skuDO.getBarCode())
                .filingNumber(skuDO.getFilingNumber())
                .status(skuDO.getStatus())
                .oaId(skuDO.getOaId())
                .syncFail(skuDO.getSyncFail())
                .specification(skuDO.getSpecification())
                .gift(skuDO.getGift())
                .card(skuDO.getCard())
                .businessInfo(skuDO.getBusinessInfo())
                .productInfo(skuDO.getProductInfo())
                .supplyChainInfo(skuDO.getSupplyChainInfo())
                .fairPrice(skuDO.getFairPrice())
                .basePrice(skuDO.getBasePrice())
                .cost(skuDO.getCost())
                .createTime(skuDO.getCreateTime())
                .updateTime(skuDO.getUpdateTime())
                .creatorId(skuDO.getCreatorId())
                .updaterId(skuDO.getUpdaterId())
                .build();
    }

    private static SkuListBO convert(SkuDO skuDO) {
        return SkuListBO.builder()
                .id(skuDO.getId())
                .skuCode(skuDO.getSkuCode())
                .skuName(skuDO.getSkuName())
                .spuId(skuDO.getSpuId())
                .barCode(skuDO.getBarCode())
                .filingNumber(skuDO.getFilingNumber())
                .status(StringUtils.hasText(skuDO.getStatus()) ? GoodsStatusEnum.valueOf(skuDO.getStatus()) : null)
                .specification(skuDO.getSpecification())
                .oaId(skuDO.getOaId())
                .syncFail(skuDO.getSyncFail())
                .specification(skuDO.getSpecification())
                .gift(skuDO.getGift())
                .card(skuDO.getCard())
                .businessInfo(JSONUtil.toBean(skuDO.getBusinessInfo(), SkuBusinessInfoValueBO.class))
                .productInfo(JSONUtil.toBean(skuDO.getProductInfo(), SkuProductInfoValueBO.class))
                .supplyChainInfo(JSONUtil.toBean(skuDO.getSupplyChainInfo(), SkuSupplyChainInfoValueBO.class))
                .fairPrice(skuDO.getFairPrice())
                .basePrice(skuDO.getBasePrice())
                .cost(skuDO.getCost())
                .createTime(skuDO.getCreateTime())
                .updateTime(skuDO.getUpdateTime())
                .creatorId(skuDO.getCreatorId())
                .updaterId(skuDO.getUpdaterId())
                .build();
    }


    /**
     * 将SKU创建命令转换为新增请求对象
     *
     * @param command SKU创建命令
     * @return 新增请求对象
     */
    public static SkuAddReq convertCommandToAddReq(SkuCreateCommand command, Long id) {
        SkuAddReq addReq = new SkuAddReq();
        addReq.setId(id);
        // 设置基础信息
        addReq.setSkuCode(command.getSkuCode());
        addReq.setSpecification(command.getSpecification());
        addReq.setSkuName(command.getSkuName());
        addReq.setStatus(command.getOaStatus().name());
        addReq.setCreatorId(command.getCreatorId());
        addReq.setUpdaterId(command.getUpdaterId());
        if (command.getBusinessInfo() != null) {
            addReq.setBusinessInfo(JsonUtils.toJsonString(command.getBusinessInfo()));
        }

        return addReq;
    }

    public static SkuUpdateReq convertCommandToUpdateReq(@NotNull SkuUpdateCommand command) {
        return SkuUpdateReq.builder()
                .id(command.getId())
                .spuId(command.getSpuId())
                .skuCode(command.getSkuCode())
                .skuName(command.getSkuName())
                .barCode(command.getBarCode())
                .filingNumber(command.getFilingNumber())
                .status(ObjectUtil.isNull(command.getStatus()) ? null : command.getStatus().name())
                .oaId(command.getOaId())
                .syncFail(command.getSyncFail())
                .specification(command.getSpecification())
                .gift(command.getGift())
                .card(command.getCard())
                .businessInfo(ObjectUtil.isNull(command.getBusinessInfo()) ? null :
                        JsonUtils.toJsonString(command.getBusinessInfo()))
                .productInfo(ObjectUtil.isNull(command.getProductInfo()) ? null :
                        JsonUtils.toJsonString(command.getProductInfo()))
                .supplyChainInfo(ObjectUtil.isNull(command.getSupplyChainInfo()) ? null :
                        JsonUtils.toJsonString(command.getSupplyChainInfo()))
                .fairPrice(command.getFairPrice())
                .basePrice(command.getBasePrice())
                .cost(command.getCost())
                .updaterId(command.getUpdaterId())
                .build();
    }
}
