package com.spes.sop.goods.mapper.model.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品分类新增请求对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsCategoryAddReq {

    /**
     * 分类主键ID（数据库自动生成）
     */
    private Long id;

    /**
     * 产品分类名称
     */
    private String name;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 更新人ID
     */
    private Long updaterId;
} 
