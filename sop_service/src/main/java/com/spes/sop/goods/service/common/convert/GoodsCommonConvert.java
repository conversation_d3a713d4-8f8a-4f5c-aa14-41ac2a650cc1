package com.spes.sop.goods.service.common.convert;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.spes.sop.goods.mapper.model.entity.GoodsCategoryDO;
import com.spes.sop.goods.mapper.model.entity.GoodsChannelDO;
import com.spes.sop.goods.mapper.model.entity.GoodsClassificationDO;
import com.spes.sop.goods.mapper.model.entity.GoodsSeriesDO;
import com.spes.sop.goods.service.common.model.bo.GoodsCommonBO;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商品通用转换器
 *
 * <AUTHOR>
 */
public class GoodsCommonConvert {

    /**
     * 渠道DO转换为通用BO
     *
     * @param result 渠道DO列表
     * @return 通用BO列表
     */
    public static List<GoodsCommonBO> channelConvert(List<GoodsChannelDO> result) {
        if (CollUtil.isEmpty(result)) {
            return Lists.newArrayList();
        }

        return result.stream()
                .filter(Objects::nonNull)
                .map(channelDO -> GoodsCommonBO.builder()
                        .id(channelDO.getId())
                        .name(channelDO.getName())
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 系列DO转换为通用BO
     *
     * @param result 系列DO列表
     * @return 通用BO列表
     */
    public static List<GoodsCommonBO> seriesConvert(List<GoodsSeriesDO> result) {
        if (CollUtil.isEmpty(result)) {
            return Lists.newArrayList();
        }

        return result.stream()
                .filter(Objects::nonNull)
                .map(seriesDO -> GoodsCommonBO.builder()
                        .id(seriesDO.getId())
                        .name(seriesDO.getName())
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 分类DO转换为通用BO
     *
     * @param result 分类DO列表
     * @return 通用BO列表
     */
    public static List<GoodsCommonBO> classificationConvert(List<GoodsClassificationDO> result) {
        if (CollUtil.isEmpty(result)) {
            return Lists.newArrayList();
        }

        return result.stream()
                .filter(Objects::nonNull)
                .map(classificationDO -> GoodsCommonBO.builder()
                        .id(classificationDO.getId())
                        .name(classificationDO.getName())
                        .build())
                .collect(Collectors.toList());
    }

    public static List<GoodsCommonBO> categoryConvert(List<GoodsCategoryDO> result) {
        if (CollUtil.isEmpty(result)) {
            return Lists.newArrayList();
        }

        return result.stream()
                .filter(Objects::nonNull)
                .map(categoryDO -> GoodsCommonBO.builder()
                        .id(categoryDO.getId())
                        .name(categoryDO.getName())
                        .build())
                .collect(Collectors.toList());
    }
}
