package com.spes.sop.goods.mapper.model.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分类更新请求对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClassificationUpdateReq {

    /**
     * 分类ID
     */
    private Long id;

    /**
     * 类目名称
     */
    private String name;

    /**
     * 父类目ID
     */
    private Long parentId;

    /**
     * 类目层级
     */
    private Integer classLevel;

    /**
     * 更新人ID
     */
    private Long updaterId;
} 
