package com.spes.sop.task.convert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.spes.sop.common.enums.GoodsStatusEnum;
import com.spes.sop.config.service.model.bo.GoodsClassificationBO;
import com.spes.sop.goods.service.combination.model.bo.CombinationListBO;
import com.spes.sop.goods.service.sku.model.bo.SkuListBO;
import com.spes.sop.goods.service.sku.model.bo.value.SkuBusinessInfoValueBO;
import com.spes.sop.goods.service.sku.model.bo.value.SkuProductInfoValueBO;
import com.spes.sop.goods.service.sku.model.bo.value.SkuSupplyChainInfoValueBO;
import com.spes.sop.goods.service.sku.model.command.SkuUpdateCommand;
import com.spes.sop.goods.service.spu.model.bo.SpuBO;
import com.spes.sop.third.jackyun.model.request.JackYunCombinationCreateReq;
import com.spes.sop.third.kingdee.model.business.enums.KingdeeInventoryTypeEnum;
import com.spes.sop.third.kingdee.model.business.enums.KingdeeMaterialAttributeEnum;
import com.spes.sop.third.kingdee.model.business.request.MaterialSaveReq;
import com.spes.sop.third.weaver.model.response.DetailTableInfo;
import com.spes.sop.third.weaver.model.response.TargetFieldInfo;
import com.spes.sop.third.weaver.model.response.WorkflowResult;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public interface ThirdConvert {
    /**
     * 组装 sku 更新信息
     */
    static SkuUpdateCommand convertSkuUpdateCommand(WorkflowResult workflowResult, SkuListBO sku, SpuBO spu,
                                                    Map<String, TargetFieldInfo> productInfo) {
        //将上面的 WorkflowResult 帮我转换成为 SkuUpdateCommand
        if (ObjectUtil.isNull(workflowResult) || CollUtil.isEmpty(workflowResult.getWorkflowDetailTableInfos())) {
            return null;
        }
        List<DetailTableInfo> detailTableInfos = workflowResult.getWorkflowDetailTableInfos();
        if (CollUtil.isEmpty(detailTableInfos)) {
            return null;
        }
        if (productInfo == null) return null;
        if (CollUtil.isEmpty(productInfo)) {
            return null;
        }
        if (ObjectUtil.isNull(spu)) {
            return SkuUpdateCommand.builder()
                    .id(sku.getId())
                    .status(GoodsStatusEnum.WEAVER_SYNC_FAILED)
                    .syncFail("未找到对应的 SPU 信息")
                    .build();
        }

        //供应链信息、运营信息、产品信息
        SkuSupplyChainInfoValueBO supplyChain = getSkuSupplyChainInfoValueBO(sku, detailTableInfos);
        SkuBusinessInfoValueBO business = getBusinessInfo(sku, detailTableInfos);
        SkuProductInfoValueBO product = getProductInfo(sku, detailTableInfos);
        if (supplyChain == null || business == null || product == null) {
            return SkuUpdateCommand.builder()
                    .id(sku.getId())
                    .status(GoodsStatusEnum.WEAVER_SYNC_FAILED)
                    .syncFail("未找到对应的供应链信息、运营信息、产品信息")
                    .build();
        }
        //组装
        return SkuUpdateCommand.builder()
                .id(sku.getId())
                .status(GoodsStatusEnum.APPROVED)
                .spuId(spu.getId())
                .specification(Optional.ofNullable(productInfo.get("spec")).map(TargetFieldInfo::getFieldValue).map(Integer::valueOf).orElse(null))
                .skuCode(Optional.ofNullable(productInfo.get("skuCode")).map(TargetFieldInfo::getFieldValue).orElse(sku.getSkuCode()))
                .skuName(Optional.ofNullable(productInfo.get("skuName")).map(TargetFieldInfo::getFieldValue).orElse(sku.getSkuName()))
                .barCode(Optional.ofNullable(productInfo.get("barCode")).map(TargetFieldInfo::getFieldValue).orElse(sku.getBarCode()))
                .filingNumber(Optional.ofNullable(productInfo.get("filingNumber")).map(TargetFieldInfo::getFieldValue).orElse(sku.getFilingNumber()))
                .gift(Optional.ofNullable(productInfo.get("gift")).map(TargetFieldInfo::getFieldValue).map(Integer::valueOf).orElse(null))
                .card(Optional.ofNullable(productInfo.get("packageCard")).map(TargetFieldInfo::getFieldValue).map(Integer::valueOf).orElse(null))
                .fairPrice(Optional.ofNullable(productInfo.get("fairPrice")).map(a -> a.getFieldValue().replace(",",
                        "")).map(Double::parseDouble).map(BigDecimal::valueOf).orElse(null))
                .basePrice(Optional.ofNullable(productInfo.get("basePrice")).map(a -> a.getFieldValue().replace(",",
                        "")).map(Double::parseDouble).map(BigDecimal::valueOf).orElse(null))
                .productInfo(product)
                .businessInfo(business)
                .supplyChainInfo(supplyChain)
                .updaterId(sku.getUpdaterId())
                .build();
    }

    static Map<String, TargetFieldInfo> getProductTableField(SkuListBO sku, List<DetailTableInfo> detailTableInfos) {
        DetailTableInfo productTable =
                detailTableInfos.stream().filter(a -> "formtable_main_246_dt2".equals(a.getTablename())).findFirst().orElse(null);
        if (ObjectUtil.isNull(productTable) || CollUtil.isEmpty(productTable.getValue())) {
            return null;
        }
        Map<String, TargetFieldInfo> productInfo =
                Optional.of(productTable.getValue().stream().filter(a -> sku.getSkuCode().equals(a.get(
                        "skuCode").getFieldValue())).findFirst()).get().orElse(null);
        return productInfo;
    }

    static SkuProductInfoValueBO getProductInfo(SkuListBO sku, List<DetailTableInfo> detailTableInfos) {
        DetailTableInfo productTable =
                detailTableInfos.stream().filter(a -> "formtable_main_246_dt2".equals(a.getTablename())).findFirst().orElse(null);
        if (ObjectUtil.isNull(productTable) || CollUtil.isEmpty(productTable.getValue())) {
            return null;
        }
        SkuProductInfoValueBO product = null;
        Map<String, TargetFieldInfo> productInfo =
                Optional.of(productTable.getValue().stream().filter(a -> sku.getSkuCode().equals(a.get(
                        "skuCode").getFieldValue())).findFirst()).get().orElse(null);
        if (CollUtil.isNotEmpty(productInfo)) {
            product = SkuProductInfoValueBO.builder()
                    .requiredArrivalDate(ObjectUtil.isNotNull(productInfo.get("requiredArrivalDate")) ?
                            DateUtil.parse(productInfo.get("requiredArrivalDate").getFieldValue()) : null)
                    .fairPrice(ObjectUtil.isNotNull(productInfo.get("fairPrice")) ?
                            BigDecimal.valueOf(Double.parseDouble(productInfo.get("fairPrice").getFieldValue().replace(",", ""))) :
                            null)
                    .basePrice(ObjectUtil.isNotNull(productInfo.get("basePrice")) ?
                            BigDecimal.valueOf(Double.parseDouble(productInfo.get("basePrice").getFieldValue().replace(",", ""))) :
                            null)
                    .finalPrice(ObjectUtil.isNotNull(productInfo.get("finalPrice")) ?
                            BigDecimal.valueOf(Double.parseDouble(productInfo.get("finalPrice").getFieldValue().replace(",", ""))) :
                            null)
                    .build();
        }
        return product;
    }

    static SkuBusinessInfoValueBO getBusinessInfo(SkuListBO sku, List<DetailTableInfo> detailTableInfos) {
        DetailTableInfo businessTable =
                detailTableInfos.stream().filter(a -> "formtable_main_246_dt1".equals(a.getTablename())).findFirst().orElse(null);
        if (ObjectUtil.isNull(businessTable) || CollUtil.isEmpty(businessTable.getValue())) {
            return null;
        }
        SkuBusinessInfoValueBO business = null;
        Map<String, TargetFieldInfo> businessInfo =
                Optional.of(businessTable.getValue().stream().filter(a -> sku.getSkuCode().equals(a.get(
                        "goods").getFieldValue())).findFirst()).get().orElse(null);
        if (CollUtil.isNotEmpty(businessInfo)) {
            business = SkuBusinessInfoValueBO.builder()
                    .applicationDate(ObjectUtil.isNotNull(businessInfo.get("applicationDate")) ?
                            DateUtil.parse(businessInfo.get("applicationDate").getFieldValue()) : null)
                    .estimatedPrice(ObjectUtil.isNotNull(businessInfo.get("estimatedPrice")) ?
                            BigDecimal.valueOf(Double.parseDouble(businessInfo.get("estimatedPrice").getFieldValue().replace(",", ""))) : null)
                    .exclusiveSale(ObjectUtil.isNotNull(businessInfo.get("exclusiveSale")) && "1".equals(businessInfo.get("exclusiveSale").getFieldValue()))
                    .factoryMinOrderQuantity(ObjectUtil.isNotNull(businessInfo.get("factoryMinOrderQuantity")) ?
                            Integer.valueOf(businessInfo.get("factoryMinOrderQuantity").getFieldValue()) : null)
                    .firstBatchQuantity(ObjectUtil.isNotNull(businessInfo.get("firstBatchQuantity")) ?
                            Integer.valueOf(businessInfo.get("firstBatchQuantity").getFieldValue()) : null)
                    .launchDate(ObjectUtil.isNotNull(businessInfo.get("launchDate")) ?
                            DateUtil.parse(businessInfo.get("launchDate").getFieldValue()) : null)
                    .minOrderQuantity(ObjectUtil.isNotNull(businessInfo.get("minOrderQuantity")) ?
                            Integer.valueOf(businessInfo.get("minOrderQuantity").getFieldValue()) : null)
                    .minOrderReason(Optional.ofNullable(businessInfo.get("minOrderReason")).map(TargetFieldInfo::getFieldValue).orElse(null))
                    .productPurpose(Optional.ofNullable(businessInfo.get("productPurpose")).map(TargetFieldInfo::getFieldValue).orElse(null))
                    .shelfLifeRequirement(Optional.ofNullable(businessInfo.get("shelfLifeRequirement")).map(TargetFieldInfo::getFieldValue).orElse(null))
                    .remark(Optional.ofNullable(businessInfo.get("remark")).map(TargetFieldInfo::getFieldValue).orElse(null))
                    .salesResponsibleEmployeeId(Optional.ofNullable(businessInfo.get("salesResponsibleEmployeeId")).map(TargetFieldInfo::getFieldValue).orElse(null))
                    .channelExclusive(ObjectUtil.isNotNull(businessInfo.get("sfqdtgp")) && "1".equals(businessInfo.get("sfqdtgp").getFieldValue()))
                    .attachments(Collections.emptyList())
                    .build();
        }
        return business;
    }

    static SkuSupplyChainInfoValueBO getSkuSupplyChainInfoValueBO(SkuListBO sku,
                                                                  List<DetailTableInfo> detailTableInfos) {
        DetailTableInfo supplyChainTable =
                detailTableInfos.stream().filter(a -> "formtable_main_246_dt3".equals(a.getTablename())).findFirst().orElse(null);
        if (ObjectUtil.isNull(supplyChainTable) || CollUtil.isEmpty(supplyChainTable.getValue())) {
            return null;
        }
        SkuSupplyChainInfoValueBO supplyChain = null;
        Map<String, TargetFieldInfo> supplyChainInfo =
                Optional.of(supplyChainTable.getValue().stream().filter(a -> sku.getSkuCode().equals(a.get(
                        "skuCode").getFieldValue())).findFirst()).get().orElse(null);
        if (CollUtil.isNotEmpty(supplyChainInfo)) {
            supplyChain = SkuSupplyChainInfoValueBO.builder()
                    .inventoryType(Optional.ofNullable(supplyChainInfo.get("inventoryType")).map(TargetFieldInfo::getFieldShowValue).orElse(null))
                    .materialAttribute(Optional.ofNullable(supplyChainInfo.get("materialAttribute")).map(TargetFieldInfo::getFieldShowValue).orElse(null))
                    .specificationModel(Optional.ofNullable(supplyChainInfo.get("specificationModel")).map(TargetFieldInfo::getFieldValue).orElse(null))
                    .materialGroup(Optional.ofNullable(supplyChainInfo.get("wlfz")).map(TargetFieldInfo::getFieldValue).orElse(null))
                    .build();
        }
        return supplyChain;
    }

    /**
     * 组装吉客云组合品创建请求信息
     */
    static JackYunCombinationCreateReq buildCombinationCreateReq(CombinationListBO combination,
                                                                 List<SkuListBO> skus) {
        if (ObjectUtil.isNull(combination) || CollUtil.isEmpty(combination.getSkuIds()) || CollUtil.isEmpty(skus)) {
            return null;
        }
        Map<Long, SkuListBO> skuMap = skus.stream().collect(Collectors.toMap(SkuListBO::getId, sku -> sku));
        List<Long> skuIds = combination.getSkuIds();
        return JackYunCombinationCreateReq.builder()
                .goodsName(combination.getName())
                .goodsNo(combination.getCombinationCode())
                .unitName("件")
                .packageSkuList(skuIds.stream().map(skuId -> {
                    SkuListBO sku = skuMap.get(skuId);
                    return JackYunCombinationCreateReq.JkyCombinationSku.builder()
                            .skuBarcode(sku.getBarCode())
                            .goodsAmount(1)
                            .sharePrice(sku.getFairPrice())
                            .build();
                }).collect(Collectors.toList()))
                .build();
    }

    /**
     * 组装金蝶物料保存的请求体
     */
    static MaterialSaveReq buildKingdeeMaterialPayload(SkuListBO sku, GoodsClassificationBO classificationBO) {
        SkuSupplyChainInfoValueBO supplyChainInfo = sku.getSupplyChainInfo();
        return MaterialSaveReq.builder()
                .materialGroup(String.valueOf(classificationBO.getId()))
                .skuCode(sku.getSkuCode())
                .skuName(sku.getSkuName())
                .barCode(sku.getBarCode())
                .materialAttribute(KingdeeMaterialAttributeEnum.getByDesc(supplyChainInfo.getMaterialAttribute()))
                .inventoryType(KingdeeInventoryTypeEnum.getByDesc(supplyChainInfo.getInventoryType()))
                .specification(String.valueOf(sku.getSpecification()))
                .build();
    }


}
