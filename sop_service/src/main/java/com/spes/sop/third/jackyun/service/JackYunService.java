package com.spes.sop.third.jackyun.service;

import cn.hutool.json.JSONUtil;
import com.spes.sop.third.jackyun.client.JackYunOpenHttpClient;
import com.spes.sop.third.jackyun.model.request.JackYunCombinationCreateReq;
import com.spes.sop.third.jackyun.model.response.JackYunResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class JackYunService {

    private static final String PACKAGE_GOODS_IMPORT = "erp.packagegoods.import";

    /**
     * 吉客云创建组合装
     *
     * @param req
     */
    public String createCombination(JackYunCombinationCreateReq req) {

        Map<String, Object> packageGoodsData = buildPackageGoodsData(req);
        log.info("组合装导入请求数据: {}", packageGoodsData);

        String response = JackYunOpenHttpClient.post(
                PACKAGE_GOODS_IMPORT,
                "1.0",
                null,
                packageGoodsData
        );
        //解析响应结果
        JackYunResponse result = JSONUtil.toBean(response, JackYunResponse.class);
        if (result.getCode() == 200) {
            return null;
        }
        return result.getMsg();
    }

    private Map<String, Object> buildPackageGoodsData(JackYunCombinationCreateReq req) {
        Map<String, Object> data = new HashMap<>();

        // 组合装基本信息（必填项）
        data.put("goodsNo", req.getGoodsNo());
        data.put("goodsName", req.getGoodsName());
        data.put("unitName", req.getUnitName());

        // 组合装子商品列表（必填项）
        List<Map<String, Object>> subGoodsList = new ArrayList<>();

        for (JackYunCombinationCreateReq.JkyCombinationSku jkyCombinationSku : req.getPackageSkuList()) {
            Map<String, Object> subGoods = new HashMap<>();
            subGoods.put("skuBarcode", jkyCombinationSku.getSkuBarcode());
            subGoods.put("goodsAmount", jkyCombinationSku.getGoodsAmount());
            subGoods.put("sharePrice", jkyCombinationSku.getSharePrice());
            subGoodsList.add(subGoods);
        }
        data.put("packageSkuList", subGoodsList);
        return data;
    }
}
