package com.spes.sop.third.weaver.constants;

/**
 * 泛微工作流常量类
 *
 * <AUTHOR>
 */
public final class WeaverWorkflowConstant {

    /**
     * 工作流ID常量
     */
    public static final class WorkflowId {

        /**
         * SKU立项审批工作流ID - 根据您实际的泛微流程ID配置
         */
        public static final String SKU_APPROVAL_WORKFLOW_ID = "113";

        /**
         * 货品组合审批工作流 id
         */
        public static final String COMBINATION_APPROVAL_WORKFLOW_ID = "115";
    }

    /**
     * 泛微结束节点 id
     */
    public static final class EndNodeName {
        public static final String END_NODE_NAME = "商品管理建档";
    }




    /**
     * 定时任务配置常量
     */
    public static final class Schedule {
        /**
         * 每 5 分钟执行一次
         */
        public static final String FIVE_MIN_CRON = "0 */5 * * * *";
        /**
         * 每 t 分钟执行一次
         */
        public static final String FIVE_MIN_CRON_2 = "4 */5 * * * *";
    }

    /**
     * 私有构造函数，防止实例化
     */
    private WeaverWorkflowConstant() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
} 