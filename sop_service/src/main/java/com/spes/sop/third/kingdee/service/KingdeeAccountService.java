/*
 * Copyright(C) 2024 Spes Co., Ltd. All rights reserved.
 */

package com.spes.sop.third.kingdee.service;

import cn.hutool.core.util.StrUtil;
import com.spes.sop.config.service.GoodsBrandService;
import com.spes.sop.config.service.model.bo.GoodsBrandBO;
import com.spes.sop.third.kingdee.client.K3CloudTemplate;
import com.spes.sop.third.kingdee.config.KingdeeConfig;
import com.spes.sop.third.kingdee.model.base.Authentication;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 金蝶账套管理服务
 * 负责管理多品牌对应的不同账套
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class KingdeeAccountService {

    private final KingdeeConfig kingdeeConfig;
    private final GoodsBrandService goodsBrandService;
    
    /**
     * 缓存不同账套的K3CloudTemplate实例
     * Key: 账套ID, Value: K3CloudTemplate实例
     * 
     * 缓存策略说明：
     * 1. 当第一次请求某个账套ID的模板时，会创建新的K3CloudTemplate实例并缓存
     * 2. 后续相同账套ID的请求会直接从缓存中获取，避免重复创建连接
     * 3. 使用ConcurrentHashMap保证线程安全
     */
    private final ConcurrentHashMap<String, K3CloudTemplate> templateCache = new ConcurrentHashMap<>();

    /**
     * 根据品牌名称获取对应的K3CloudTemplate
     * 
     * @param brandName 品牌名称
     * @return K3CloudTemplate实例
     */
    public K3CloudTemplate getTemplateByBrand(String brandName) {
        String acctId = getAcctIdByBrand(brandName);
        return getTemplateByAcctId(acctId);
    }

    /**
     * 根据品牌ID获取对应的K3CloudTemplate
     * 
     * @param brandId 品牌ID
     * @return K3CloudTemplate实例
     */
    public K3CloudTemplate getTemplateByBrandId(Long brandId) {
        if (brandId == null) {
            log.warn("品牌ID为空，使用默认账套");
            return getTemplateByBrand("默认");
        }
        
        GoodsBrandBO brand = goodsBrandService.getBrand(brandId);
        if (brand == null) {
            log.warn("未找到品牌信息，品牌ID: {}，使用默认账套", brandId);
            return getTemplateByBrand("默认");
        }
        
        return getTemplateByBrand(brand.getName());
    }

    /**
     * 根据账套ID获取对应的K3CloudTemplate
     * 
     * 🔥 这里是缓存的核心逻辑！
     * 
     * @param acctId 账套ID
     * @return K3CloudTemplate实例
     */
    public K3CloudTemplate getTemplateByAcctId(String acctId) {
        log.debug("尝试获取账套ID [{}] 的模板实例", acctId);
        
        // 🔥 缓存的关键代码：computeIfAbsent方法
        // 1. 如果缓存中已存在该账套ID的模板，直接返回缓存的实例
        // 2. 如果缓存中不存在，则调用createTemplate方法创建新实例，并自动放入缓存
        K3CloudTemplate template = templateCache.computeIfAbsent(acctId, this::createTemplate);
        
        log.debug("成功获取账套ID [{}] 的模板实例，当前缓存大小: {}", acctId, templateCache.size());
        return template;
    }

    /**
     * 根据品牌名称获取对应的账套ID
     * 
     * @param brandName 品牌名称
     * @return 账套ID
     */
    public String getAcctIdByBrand(String brandName) {
        if (StrUtil.isBlank(brandName)) {
            log.warn("品牌名称为空，使用默认账套");
            return kingdeeConfig.getAcctIdByBrand("默认");
        }
        
        String acctId = kingdeeConfig.getAcctIdByBrand(brandName);
        log.debug("品牌 [{}] 对应的账套ID: {}", brandName, acctId);
        return acctId;
    }

    /**
     * 创建K3CloudTemplate实例
     * 
     * 🔥 这是实际创建模板实例的方法，只有在缓存中不存在时才会被调用
     * 
     * @param acctId 账套ID
     * @return K3CloudTemplate实例
     */
    private K3CloudTemplate createTemplate(String acctId) {
        log.info("🔥 创建新的金蝶模板实例，账套ID: {}", acctId);
        
        // 创建认证对象
        Authentication auth = new Authentication(acctId, 
                kingdeeConfig.getUsername(), 
                kingdeeConfig.getPassword());
        
        // 创建K3CloudTemplate实例
        K3CloudTemplate template = new K3CloudTemplate(kingdeeConfig.getServerUrl(), auth);
        
        log.info("✅ 金蝶模板实例创建成功，账套ID: {}，当前缓存将包含 {} 个实例", 
                acctId, templateCache.size() + 1);
        
        return template;
    }

    /**
     * 清除缓存的模板实例
     * 主要用于配置更新后的清理
     */
    public void clearTemplateCache() {
        int cacheSize = templateCache.size();
        templateCache.clear();
        log.info("🧹 清除金蝶模板缓存完成，已清理 {} 个实例", cacheSize);
    }

    /**
     * 获取默认的K3CloudTemplate（向后兼容）
     * 
     * @return 默认的K3CloudTemplate实例
     */
    public K3CloudTemplate getDefaultTemplate() {
        return getTemplateByAcctId(kingdeeConfig.getAcctId());
    }
    
    /**
     * 获取当前缓存的统计信息
     * 
     * @return 缓存统计信息
     */
    public String getCacheStats() {
        return String.format("缓存统计 - 总实例数: %d, 账套列表: %s", 
                templateCache.size(), 
                templateCache.keySet());
    }
    
    /**
     * 检查指定账套ID是否已缓存
     * 
     * @param acctId 账套ID
     * @return 是否已缓存
     */
    public boolean isCached(String acctId) {
        return templateCache.containsKey(acctId);
    }
} 