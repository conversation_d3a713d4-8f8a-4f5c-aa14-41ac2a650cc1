/*
 * Copyright(C) 2024 Spes Co., Ltd. All rights reserved.
 */

package com.spes.sop.third.kingdee.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 金蝶云星空配置类
 * 从配置文件中读取金蝶云星空相关配置参数
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "kingdee")
public class KingdeeConfig {

    /**
     * 服务器地址（对应yml中的server-url）
     * 示例: http://*************/k3cloud/
     */
    private String serverUrl = "http://localhost/k3cloud/";

    /**
     * 账套ID/数据库ID（对应yml中的acct-id）
     * 这是金蝶云星空中的账套标识符
     */
    private String acctId = "66d67292cd0454";

    /**
     * 多品牌账套配置（对应yml中的accounts）
     * Key: 品牌名称, Value: 账套ID
     */
    private Map<String, String> accounts;

    /**
     * 用户名（对应yml中的username）
     * 用于登录金蝶云星空的用户账号
     */
    private String username = "admin";

    /**
     * 密码（对应yml中的password）
     * 用于登录金蝶云星空的用户密码
     */
    private String password = "admin";

    /**
     * 语言ID（对应yml中的lcid）
     * 2052为中文简体，1033为英文，3076为繁体中文
     * 默认为中文简体
     */
    private Integer lcid = 2052;

    /**
     * 连接超时时间（毫秒）
     * 建议不少于30秒，默认30秒
     */
    private Integer connectTimeout = 30000;

    /**
     * 读取超时时间（毫秒）
     * 建议不少于60秒，默认60秒
     */
    private Integer readTimeout = 60000;

    /**
     * Session过期时间（毫秒）
     * 默认30分钟，建议根据实际使用情况调整
     * 注意：实际会话时间可能受到金蝶服务器配置影响
     */
    private Long sessionExpireTime = 1800000L; // 30分钟

    /**
     * 根据品牌名称获取对应的账套ID
     *
     * @param brandName 品牌名称
     * @return 账套ID，如果未找到则返回默认账套ID
     */
    public String getAcctIdByBrand(String brandName) {
        if (accounts == null || accounts.isEmpty()) {
            return acctId;
        }

        // 优先使用品牌对应的账套ID
        String brandAcctId = accounts.get(brandName);
        if (brandAcctId != null) {
            return brandAcctId;
        }

        // 如果没有找到品牌对应的账套，尝试使用"默认"账套
        String defaultAcctId = accounts.get("default");
        if (defaultAcctId != null) {
            return defaultAcctId;
        }
        // 最后返回配置的默认账套ID
        return acctId;
    }
} 