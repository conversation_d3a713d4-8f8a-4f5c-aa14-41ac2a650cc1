/*
 * Copyright(C) 2024 Spes Co., Ltd. All rights reserved.
 */

package com.spes.sop.third.kingdee.service;

import cn.hutool.core.util.StrUtil;
import com.spes.sop.third.kingdee.client.K3CloudTemplate;
import com.spes.sop.third.kingdee.config.KingdeeConfig;
import com.spes.sop.third.kingdee.model.base.Authentication;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 金蝶账套管理服务
 * 负责管理多品牌对应的不同账套
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
public class KingdeeAccountUtil {

    private final KingdeeConfig kingdeeConfig;

    
    /**
     * 缓存不同账套的K3CloudTemplate实例
     * Key: 账套ID, Value: K3CloudTemplate实例
     */
    private final ConcurrentHashMap<String, K3CloudTemplate> templateCache = new ConcurrentHashMap<>();

    /**
     * 根据品牌名称获取对应的K3CloudTemplate
     * 
     * @param brandName 品牌名称
     * @return K3CloudTemplate实例
     */
    public K3CloudTemplate getTemplateByBrand(String brandName) {
        String acctId = getAcctIdByBrand(brandName);
        return getTemplateByAcctId(acctId);
    }


    /**
     * 根据账套ID获取对应的K3CloudTemplate
     * 
     * @param acctId 账套ID
     * @return K3CloudTemplate实例
     */
    public K3CloudTemplate getTemplateByAcctId(String acctId) {
        return templateCache.computeIfAbsent(acctId, this::createTemplate);
    }

    /**
     * 根据品牌名称获取对应的账套ID
     * 
     * @param brandName 品牌名称
     * @return 账套ID
     */
    public String getAcctIdByBrand(String brandName) {
        if (StrUtil.isBlank(brandName)) {
            log.warn("品牌名称为空，使用默认账套");
            return kingdeeConfig.getAcctIdByBrand("默认");
        }
        
        String acctId = kingdeeConfig.getAcctIdByBrand(brandName);
        log.debug("品牌 [{}] 对应的账套ID: {}", brandName, acctId);
        return acctId;
    }

    /**
     * 创建K3CloudTemplate实例
     * 
     * @param acctId 账套ID
     * @return K3CloudTemplate实例
     */
    private K3CloudTemplate createTemplate(String acctId) {
        log.info("创建金蝶模板实例，账套ID: {}", acctId);
        Authentication auth = new Authentication(acctId, 
                kingdeeConfig.getUsername(), 
                kingdeeConfig.getPassword());
        return new K3CloudTemplate(kingdeeConfig.getServerUrl(), auth);
    }

    /**
     * 清除缓存的模板实例
     * 主要用于配置更新后的清理
     */
    public void clearTemplateCache() {
        log.info("清除金蝶模板缓存");
        templateCache.clear();
    }

} 