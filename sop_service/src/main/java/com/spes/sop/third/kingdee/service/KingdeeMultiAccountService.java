/*
 * Copyright(C) 2024 Spes Co., Ltd. All rights reserved.
 */

package com.spes.sop.third.kingdee.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.spes.sop.third.kingdee.client.K3CloudTemplate;
import com.spes.sop.third.kingdee.model.base.*;
import com.spes.sop.third.kingdee.model.business.convert.BusinessKingdeeConvert;
import com.spes.sop.third.kingdee.model.business.entity.KingdeeCustomerBO;
import com.spes.sop.third.kingdee.model.business.request.MaterialSaveReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 金蝶多账套业务服务
 * 支持根据品牌动态选择不同的账套进行操作
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class KingdeeMultiAccountService {

    private final KingdeeAccountService kingdeeAccountService;

    /**
     * 金蝶物料
     */
    private static final String MATERIAL_FORM_ID = "BD_MATERIAL";

    /**
     * 根据品牌名称保存金蝶物料
     *
     * @param brandName 品牌名称
     * @param req 物料保存请求
     * @return 操作结果
     * <AUTHOR>
     */
    public RequestResult saveMaterialByBrand(String brandName, MaterialSaveReq req) {
        if (ObjectUtil.isNull(req)) {
            return RequestResult.builder().success(false).errorMessage("请求参数为空").build();
        }
        
        log.info("开始保存金蝶物料，品牌: {}, 请求参数: {}", brandName, JSONUtil.toJsonStr(req));
        
        K3CloudTemplate template = kingdeeAccountService.getTemplateByBrand(brandName);
        String acctId = kingdeeAccountService.getAcctIdByBrand(brandName);
        log.info("使用账套ID: {} 保存物料", acctId);
        
        BillSave billSave = new BillSave(MATERIAL_FORM_ID);
        billSave.setModel(BusinessKingdeeConvert.buildKingdeeMaterialPayload(req));

        KingdeeResult result = template.executeBillSave(billSave, KingdeeResult.class);

        if (result != null && result.getResult() != null && result.getResult().getResponseStatus() != null) {
            ResultStatus status = result.getResult().getResponseStatus();
            if (Boolean.TRUE.equals(status.getIsSuccess())) {
                log.info("金蝶物料保存成功，品牌: {}, 账套ID: {}, 物料编号: {}", 
                        brandName, acctId, result.getResult().getNumber());
                return RequestResult.builder().success(true).build();
            } else {
                log.error("金蝶物料保存失败，品牌: {}, 账套ID: {}, 错误码: {}, 错误信息: {}",
                        brandName, acctId, status.getErrorCode(),
                        status.getErrors() != null ? JSONUtil.toJsonStr(status.getErrors()) : "无错误详情");
                return RequestResult.builder()
                        .success(false)
                        .errorMessage(status.getErrors().get(0).getMessage()).build();
            }
        } else {
            log.warn("金蝶物料保存响应结果为空或格式异常，品牌: {}, 账套ID: {}", brandName, acctId);
            return RequestResult.builder().success(false).errorMessage("金蝶物料保存响应结果为空或格式异常").build();
        }
    }

    /**
     * 根据品牌ID保存金蝶物料
     *
     * @param brandId 品牌ID
     * @param req 物料保存请求
     * @return 操作结果
     * <AUTHOR>
     */
    public RequestResult saveMaterialByBrandId(Long brandId, MaterialSaveReq req) {
        if (ObjectUtil.isNull(req)) {
            return RequestResult.builder().success(false).errorMessage("请求参数为空").build();
        }
        
        log.info("开始保存金蝶物料，品牌ID: {}, 请求参数: {}", brandId, JSONUtil.toJsonStr(req));
        
        K3CloudTemplate template = kingdeeAccountService.getTemplateByBrandId(brandId);
        String acctId = kingdeeAccountService.getAcctIdByBrand("默认"); // 这里可以根据需要优化
        log.info("使用账套ID: {} 保存物料", acctId);
        
        BillSave billSave = new BillSave(MATERIAL_FORM_ID);
        billSave.setModel(BusinessKingdeeConvert.buildKingdeeMaterialPayload(req));

        KingdeeResult result = template.executeBillSave(billSave, KingdeeResult.class);

        if (result != null && result.getResult() != null && result.getResult().getResponseStatus() != null) {
            ResultStatus status = result.getResult().getResponseStatus();
            if (Boolean.TRUE.equals(status.getIsSuccess())) {
                log.info("金蝶物料保存成功，品牌ID: {}, 账套ID: {}, 物料编号: {}", 
                        brandId, acctId, result.getResult().getNumber());
                return RequestResult.builder().success(true).build();
            } else {
                log.error("金蝶物料保存失败，品牌ID: {}, 账套ID: {}, 错误码: {}, 错误信息: {}",
                        brandId, acctId, status.getErrorCode(),
                        status.getErrors() != null ? JSONUtil.toJsonStr(status.getErrors()) : "无错误详情");
                return RequestResult.builder()
                        .success(false)
                        .errorMessage(status.getErrors().get(0).getMessage()).build();
            }
        } else {
            log.warn("金蝶物料保存响应结果为空或格式异常，品牌ID: {}, 账套ID: {}", brandId, acctId);
            return RequestResult.builder().success(false).errorMessage("金蝶物料保存响应结果为空或格式异常").build();
        }
    }

    /**
     * 根据品牌名称获取客户信息
     *
     * @param brandName 品牌名称
     * @param customerName 客户名称
     * @return 客户信息列表
     * <AUTHOR>
     */
    public List<KingdeeCustomerBO> getCustomerInfoByBrand(String brandName, String customerName) {
        K3CloudTemplate template = kingdeeAccountService.getTemplateByBrand(brandName);
        String acctId = kingdeeAccountService.getAcctIdByBrand(brandName);
        
        log.info("获取客户信息，品牌: {}, 账套ID: {}, 客户名称: {}", brandName, acctId, customerName);
        
        // 查询字段
        String[] fieldKeys = {
                "FNumber", "FName"
        };
        // 过滤条件
        String filter = "FName='" + customerName + "'";
        BillQuery query = new BillQuery("bd_customer", StringUtils.arrayToDelimitedString(fieldKeys, ","), filter);
        query.setStartRow(0);
        query.setLimit(100);
        
        List<KingdeeCustomerBO> dataList = template.executeBillQuery(query, KingdeeCustomerBO.class);
        log.info("获取客户信息结果，品牌: {}, 账套ID: {}, 结果: {}", brandName, acctId, JSONUtil.toJsonStr(dataList));
        return dataList;
    }

    /**
     * 根据品牌名称提交金蝶单据
     *
     * @param brandName 品牌名称
     * @param code 单据编号
     * @return 操作结果
     * <AUTHOR>
     */
    public RequestResult submitBillByBrand(String brandName, String code) {
        if (!StringUtils.hasText(code)) {
            log.warn("单据编号为空，跳过提交操作");
            return RequestResult.builder().success(false).errorMessage("单据编号为空").build();
        }

        K3CloudTemplate template = kingdeeAccountService.getTemplateByBrand(brandName);
        String acctId = kingdeeAccountService.getAcctIdByBrand(brandName);
        
        log.info("开始提交金蝶单据，品牌: {}, 账套ID: {}, 单据编号: {}", brandName, acctId, code);
        
        BillSubmit<String> billSubmit = new BillSubmit<>(MATERIAL_FORM_ID);
        billSubmit.setNumbers(Lists.newArrayList(code));
        KingdeeResult result = template.executeBillSubmit(billSubmit, KingdeeResult.class);

        if (result != null && result.getResult() != null && result.getResult().getResponseStatus() != null) {
            ResultStatus status = result.getResult().getResponseStatus();
            if (Boolean.TRUE.equals(status.getIsSuccess())) {
                log.info("金蝶单据提交成功，品牌: {}, 账套ID: {}", brandName, acctId);
                return RequestResult.builder().success(true).build();
            }
            log.error("金蝶单据提交失败，品牌: {}, 账套ID: {}, 错误码: {}, 错误信息: {}",
                    brandName, acctId, status.getErrorCode(),
                    status.getErrors() != null ? JSONUtil.toJsonStr(status.getErrors()) : "无错误详情");
            return RequestResult.builder().success(false).errorMessage(status.getErrors().get(0).getMessage()).build();
        }
        return RequestResult.builder().success(false).errorMessage("金蝶单据提交响应结果为空或格式异常").build();
    }

    /**
     * 根据品牌名称审核金蝶单据
     *
     * @param brandName 品牌名称
     * @param code 单据编号
     * @return 操作结果
     * <AUTHOR>
     */
    public RequestResult auditBillByBrand(String brandName, String code) {
        if (!StringUtils.hasText(code)) {
            log.warn("单据编号为空，跳过审核操作");
            return RequestResult.builder().success(false).errorMessage("单据编号为空").build();
        }

        K3CloudTemplate template = kingdeeAccountService.getTemplateByBrand(brandName);
        String acctId = kingdeeAccountService.getAcctIdByBrand(brandName);
        
        log.info("开始审核金蝶单据，品牌: {}, 账套ID: {}, 单据编号: {}", brandName, acctId, code);
        
        BillAudit<String> billAudit = new BillAudit<>(MATERIAL_FORM_ID);
        billAudit.setNumbers(Lists.newArrayList(code));
        KingdeeResult result = template.executeBillAudit(billAudit, KingdeeResult.class);
        
        if (result != null && result.getResult() != null && result.getResult().getResponseStatus() != null) {
            ResultStatus status = result.getResult().getResponseStatus();
            if (Boolean.TRUE.equals(status.getIsSuccess())) {
                log.info("金蝶单据审核成功，品牌: {}, 账套ID: {}", brandName, acctId);
                return RequestResult.builder().success(true).build();
            }
            log.error("金蝶单据审核失败，品牌: {}, 账套ID: {}, 错误码: {}, 错误信息: {}",
                    brandName, acctId, status.getErrorCode(),
                    status.getErrors() != null ? JSONUtil.toJsonStr(status.getErrors()) : "无错误详情");
            return RequestResult.builder().success(false).errorMessage(status.getErrors().get(0).getMessage()).build();
        }
        return RequestResult.builder().success(false).errorMessage("金蝶单据审核响应结果为空或格式异常").build();
    }

    /**
     * 金蝶返回结果
     * <AUTHOR>
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class KingdeeResult {
        @JsonProperty("Result")
        private Result result;
    }

    /**
     * 金蝶返回结果详情
     *
     * <AUTHOR>
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Result {
        @JsonProperty("ResponseStatus")
        private ResultStatus responseStatus;

        @JsonProperty("Id")
        private String id;

        @JsonProperty("Number")
        private String number;

        @JsonProperty("NeedReturnData")
        private java.util.List<Object> needReturnData;
    }

    /**
     * 金蝶响应状态
     *
     * <AUTHOR>
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ResultStatus {

        @JsonProperty("ErrorCode")
        private Integer errorCode;

        @JsonProperty("IsSuccess")
        private Boolean isSuccess;

        @JsonProperty("Errors")
        private List<KingdeeError> errors;

        @JsonProperty("SuccessEntitys")
        private List<Object> successEntitys;

        @JsonProperty("SuccessMessages")
        private List<String> successMessages;

        @JsonProperty("MsgCode")
        private Integer msgCode;
    }

    /**
     * 金蝶错误信息
     *
     * <AUTHOR>
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class KingdeeError {

        @JsonProperty("FieldName")
        private String fieldName;

        @JsonProperty("Message")
        private String message;

        @JsonProperty("DIndex")
        private Integer dIndex;
    }
} 