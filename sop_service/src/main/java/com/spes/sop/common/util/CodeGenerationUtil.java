package com.spes.sop.common.util;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 编码生成工具类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CodeGenerationUtil {
    /**
     * SPU编码前缀
     */
    private static final String SPU_PREFIX = "SPES";

    /**
     * SKU编码前缀
     */
    private static final String SKU_PREFIX = "SKU";

    /**
     * 组合编码前缀
     */
    private static final String COMBO_PREFIX = "ZH";


    /**
     * 生成SPU编码
     *
     * @return SPU编码
     */
    public static String generateSpuCode() {
        return SPU_PREFIX + generateCode();
    }


    /**
     * 生成货品编码
     *
     * @param specCode 规格代号
     * @return 货品编码
     */
    public static String generateSkuCode(Long specCode) {
        return SKU_PREFIX + generateCode() + "-" + specCode;
    }

    /**
     * 批量生成唯一的SKU编码
     *
     * @param specCodes 规格代号列表
     * @return 唯一的SKU编码列表
     */
    public static List<String> generateUniqueSkuCodes(List<Long> specCodes) {
        List<String> skuCodes = new ArrayList<>();
        Set<String> codeSet = new HashSet<>();

        for (Long specCode : specCodes) {
            String skuCode;
            int attempts = 0;
            // 最多尝试100次生成唯一编码
            do {
                skuCode = SKU_PREFIX + generateCode() + "-" + specCode;
                attempts++;
                if (attempts > 10) {
                    throw new RuntimeException("生成唯一SKU编码失败，请稍后重试");
                }
                // 添加微小延迟确保时间戳不同
                try {
                    Thread.sleep(1);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            } while (codeSet.contains(skuCode));

            codeSet.add(skuCode);
            skuCodes.add(skuCode);
        }

        return skuCodes;
    }

    /**
     * 生成组合编码
     * 编码规则：ZH2025040001
     *
     * @return 组合编码
     */
    public static String generateComboCode() {
        return COMBO_PREFIX + generateCode();
    }


    /**
     * 根据时间生成不重复的编码(再通过一位随机数实现唯一性)
     */
    private static String generateCode() {
        String time = new SimpleDateFormat("yyMMddHHmmssSSS").format(new Date());
        return time + (int) (Math.random() * 100);
    }
} 