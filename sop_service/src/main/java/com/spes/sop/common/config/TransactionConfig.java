package com.spes.sop.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * 事务配置类
 * 确保TransactionTemplate Bean的正确配置
 *
 * <AUTHOR>
 */
@Configuration
public class TransactionConfig {

    /**
     * 创建TransactionTemplate Bean
     * 用于编程式事务管理
     *
     * @param transactionManager 事务管理器
     * @return TransactionTemplate实例
     */
    @Bean
    public TransactionTemplate transactionTemplate(PlatformTransactionManager transactionManager) {
        return new TransactionTemplate(transactionManager);
    }
}
