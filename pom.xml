<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.spes</groupId>
  <artifactId>homaySOP</artifactId>
  <version>1.0</version>
  <packaging>pom</packaging>

  <name>homay-sop</name>
  <description>Ho<PERSON>y SOP System</description>

  <!-- 子模块声明 -->
  <modules>
    <module>sop_api</module>
    <module>sop_service</module>
  </modules>

  <!-- 属性配置 -->
  <properties>
    <java.version>8</java.version>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    
    <!-- Spring Boot版本 - Java 8兼容版本 -->
    <spring-boot.version>2.7.18</spring-boot.version>
    
    <!-- 其他依赖版本 -->
    <mysql.version>8.0.33</mysql.version>
    <druid.version>1.2.20</druid.version>
    <hutool.version>5.8.22</hutool.version>
    <fastjson2.version>2.0.43</fastjson2.version>
    <knife4j.version>3.0.3</knife4j.version>
    <jwt.version>4.4.0</jwt.version>
    <redisson.version>3.24.3</redisson.version>
    <guava.version>28.1-jre</guava.version>
    <aliyun-oss.version>3.15.1</aliyun-oss.version>

    <!-- 默认环境 -->
    <profiles.active>dev</profiles.active>
  </properties>

  <!-- Maven环境配置 -->
  <profiles>
    <!-- 开发环境 -->
    <profile>
      <id>dev</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <properties>
        <profiles.active>dev</profiles.active>
        <spring.profiles.active>dev</spring.profiles.active>
      </properties>
    </profile>

    <!-- 生产环境 -->
    <profile>
      <id>prod</id>
      <properties>
        <profiles.active>prod</profiles.active>
        <spring.profiles.active>prod</spring.profiles.active>
      </properties>
    </profile>
  </profiles>

  <!-- 依赖管理 -->
  <dependencyManagement>
    <dependencies>
      <!-- Spring Boot BOM -->
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring-boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <!-- 数据库相关 -->
      <dependency>
        <groupId>com.mysql</groupId>
        <artifactId>mysql-connector-j</artifactId>
        <version>${mysql.version}</version>
      </dependency>
      
      <dependency>
        <groupId>org.mybatis.spring.boot</groupId>
        <artifactId>mybatis-spring-boot-starter</artifactId>
        <version>2.3.1</version>
      </dependency>
      
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid-spring-boot-starter</artifactId>
        <version>${druid.version}</version>
      </dependency>

      <!-- 工具类 -->
      <dependency>
        <groupId>cn.hutool</groupId>
        <artifactId>hutool-all</artifactId>
        <version>${hutool.version}</version>
      </dependency>
      
      <dependency>
        <groupId>com.alibaba.fastjson2</groupId>
        <artifactId>fastjson2</artifactId>
        <version>${fastjson2.version}</version>
      </dependency>

      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
      </dependency>

        <!-- 阿里云OSS SDK -->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>${aliyun-oss.version}</version>
        </dependency>

        <!-- EasyExcel -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.3.2</version>
        </dependency>

        <!-- JWT -->
      <dependency>
        <groupId>com.auth0</groupId>
        <artifactId>java-jwt</artifactId>
        <version>${jwt.version}</version>
      </dependency>


    </dependencies>
  </dependencyManagement>


  <!-- 插件管理 -->
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-maven-plugin</artifactId>
          <version>${spring-boot.version}</version>
          <executions>
            <execution>
              <goals>
                <goal>repackage</goal>
              </goals>
            </execution>
          </executions>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.11.0</version>
          <configuration>
            <source>${java.version}</source>
            <target>${java.version}</target>
            <encoding>${project.build.sourceEncoding}</encoding>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

</project>
