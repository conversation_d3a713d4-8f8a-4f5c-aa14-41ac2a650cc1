package com.spes.sop.support.goods.model.response;

import com.spes.sop.controller.goods.model.request.CombinationBatchImportRequest;
import com.spes.sop.controller.goods.model.vo.CombinationBatchImportResultVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 组合品业务验证结果
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationBusinessValidationResult {

    /**
     * 有效的组合品数据（按名称分组）
     */
    private Map<String, List<CombinationBatchImportRequest>> validCombinations;

    /**
     * SKU编码到ID的映射
     */
    private Map<String, Long> skuCodeToIdMap;

    /**
     * 失败详情列表
     */
    private List<CombinationBatchImportResultVO.CombinationImportFailDetail> failDetails;
} 