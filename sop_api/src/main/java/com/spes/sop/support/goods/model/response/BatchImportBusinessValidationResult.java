package com.spes.sop.support.goods.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 通用的批量导入业务验证结果
 *
 * @param <T> 导入数据类型
 * @param <F> 失败详情类型
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchImportBusinessValidationResult<T, F> {

    /**
     * 有效的分组数据（按名称分组）
     */
    private Map<String, List<T>> validGroupedData;

    /**
     * SKU编码到ID的映射
     */
    private Map<String, Long> skuCodeToIdMap;

    /**
     * 失败详情列表
     */
    private List<F> failDetails;
} 