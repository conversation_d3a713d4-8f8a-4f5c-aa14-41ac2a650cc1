package com.spes.sop.support.goods.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 通用的批量导入验证结果
 *
 * @param <T> 导入数据类型
 * @param <F> 失败详情类型
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchImportValidationResult<T, F> {

    /**
     * 有效的数据列表
     */
    private List<T> validDataList;

    /**
     * 有效数据对应的行号列表
     */
    private List<Integer> validRowNums;

    /**
     * 失败详情列表
     */
    private List<F> failDetails;
} 