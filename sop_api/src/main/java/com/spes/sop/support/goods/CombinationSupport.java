package com.spes.sop.support.goods;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.aliyuncs.utils.StringUtils;
import com.google.common.collect.Lists;
import com.spes.sop.common.enums.GoodsStatusEnum;
import com.spes.sop.common.exception.BusinessException;
import com.spes.sop.common.page.BasePager;
import com.spes.sop.common.page.PageResult;
import com.spes.sop.common.security.JwtUserDetails;
import com.spes.sop.common.security.SecurityContextUtil;
import com.spes.sop.common.util.CodeGenerationUtil;
import com.spes.sop.config.service.*;
import com.spes.sop.config.service.model.bo.*;
import com.spes.sop.config.service.model.query.BrandBOPagerQuery;
import com.spes.sop.config.service.model.query.CategoryBOPagerQuery;
import com.spes.sop.config.service.model.query.ChannelBOPagerQuery;
import com.spes.sop.config.service.model.query.SeriesBOPagerQuery;
import com.spes.sop.controller.goods.model.query.CombinationPageQuery;
import com.spes.sop.controller.goods.model.request.CombinationBatchImportRequest;
import com.spes.sop.controller.goods.model.request.CombinationCreateRequest;
import com.spes.sop.controller.goods.model.request.CombinationUpdateRequest;
import com.spes.sop.controller.goods.model.request.value.CombinationSkuItem;
import com.spes.sop.controller.goods.model.vo.CombinationBatchImportResultVO;
import com.spes.sop.goods.service.combination.CombinationService;
import com.spes.sop.goods.service.combination.model.bo.CombinationDetailBO;
import com.spes.sop.goods.service.combination.model.bo.CombinationListBO;
import com.spes.sop.goods.service.combination.model.bo.CombinationSkuRelationBO;
import com.spes.sop.goods.service.combination.model.command.CombinationCreateCommand;
import com.spes.sop.goods.service.combination.model.command.CombinationUpdateCommand;
import com.spes.sop.goods.service.combination.model.query.CombinationBOPageQuery;
import com.spes.sop.goods.service.sku.SkuService;
import com.spes.sop.goods.service.sku.model.bo.SkuListBO;
import com.spes.sop.goods.service.sku.model.query.SkuBOListQuery;
import com.spes.sop.goods.service.spu.SpuService;
import com.spes.sop.goods.service.spu.model.bo.SpuBO;
import com.spes.sop.support.goods.converter.CombinationBatchImportConverter;
import com.spes.sop.support.goods.converter.CombinationDTOConverter;
import com.spes.sop.support.goods.model.dto.CombinationDetailDTO;
import com.spes.sop.support.goods.model.dto.CombinationPageDTO;
import com.spes.sop.support.goods.model.response.CombinationBusinessValidationResult;
import com.spes.sop.support.goods.model.response.CombinationDataValidationResult;
import com.spes.sop.third.weaver.service.WeaverWorkflowService;
import com.spes.sop.user.service.user.UserService;
import com.spes.sop.user.service.user.model.bo.UserBO;
import com.spes.sop.user.service.user.model.query.UserPageQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 组合商品支持类，提供组合商品相关的业务逻辑支持
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CombinationSupport {

    private final CombinationService combinationService;
    private final GoodsChannelService goodsChannelService;
    private final GoodsSeriesService goodsSeriesService;
    private final GoodsCategoryService goodsCategoryService;
    private final GoodsClassificationService goodsClassificationService;
    private final GoodsBrandService goodsBrandService;
    private final SkuService skuService;
    private final SpuService spuService;
    private final TransactionTemplate transactionTemplate;
    private final WeaverWorkflowService weaverWorkflowService;
    private final UserService userService;

    /**
     * 分页查询组合商品列表
     *
     * @param request 分页查询请求参数，不能为空
     * @return 分页结果，包含组合商品列表和分页信息
     * @throws IllegalArgumentException 当请求参数为空时抛出
     */
    public PageResult<CombinationPageDTO> page(@NotNull CombinationPageQuery request) {
        CombinationBOPageQuery query = CombinationDTOConverter.toServiceQuery(request);
        Long count = combinationService.count(query);
        if (ObjectUtil.isNull(count) || count <= 0) {
            return PageResult.of(Collections.emptyList(), 0L, request.getPageNum(), request.getPageSize());
        }

        List<CombinationListBO> combinations = combinationService.list(query);
        if (CollectionUtils.isEmpty(combinations)) {
            return PageResult.of(Collections.emptyList(), count, request.getPageNum(), request.getPageSize());
        }

        List<CombinationPageDTO> result = fillCombinationInfo(combinations);
        return PageResult.of(result, count, request.getPageNum(), request.getPageSize());
    }



    /**
     * 根据ID获取组合商品详情
     *
     * @param id 组合商品ID，不能为空
     * @return 组合商品详情，如果不存在则返回null
     * @throws IllegalArgumentException 当ID为空时抛出
     */
    public CombinationDetailDTO getById(@NotNull Long id) {

        CombinationDetailBO combination = combinationService.getById(id);
        if (combination == null) {
            log.debug("组合商品不存在，ID：{}", id);
            return null;
        }

        // 获取所有关联的SKU信息（包括生效和失效的）
        List<Long> allSkuIds = CollUtil.isNotEmpty(combination.getSkuRelations()) ?
                combination.getSkuRelations().stream()
                        .map(CombinationSkuRelationBO::getSkuId)
                        .distinct()
                        .collect(Collectors.toList()) : Collections.emptyList();
        List<SkuListBO> skus = getSkus(allSkuIds);

        // 更新人、创建人信息
        Long creatorId = combination.getCreatorId();
        Long updaterId = combination.getUpdaterId();
        List<UserBO> users =
                userService.getUserPage(UserPageQuery.builder().ids(Arrays.asList(creatorId, updaterId)).build());
        //属性信息
        //品牌信息
        List<GoodsBrandBO> brands = getBrands(Lists.newArrayList(combination.getBrandId()));
        List<GoodsCategoryBO> categories = getCategories(Lists.newArrayList(combination.getCategoryId()));
        List<GoodsChannelBO> channels = getChannels(Lists.newArrayList(combination.getChannelId()));
        List<GoodsSeriesBO> series = getSeries(Lists.newArrayList(combination.getSeriesId()));
        List<GoodsClassificationBO> classifications = getClassifications(Lists.newArrayList(
                combination.getFirstClassification(),
                combination.getSecondClassification(),
                combination.getThirdClassification(),
                combination.getFourthClassification()
        ));
        return CombinationDTOConverter.toDetailDTO(combination, skus, users,
                brands, categories, channels, series, classifications);
    }


    /**
     * 创建组合商品
     *
     * @param request 创建请求参数，不能为空
     * @throws IllegalArgumentException 当请求参数为空时抛出
     */
    public void create(@NotNull CombinationCreateRequest request) {
        // 获取当前操作人ID
        JwtUserDetails user = SecurityContextUtil.getCurrentUserDetails().get();
        // 参数校验
        validateCreateRequest(request);
        // 校验并获取主品信息
        CombinationSkuItem mainSku = request.getSkuIds().stream()
                .filter(CombinationSkuItem::getMain)
                .findFirst()
                .orElseThrow(() -> new BusinessException("主品不能为空"));

        // 校验所有SKU是否存在
        List<SkuListBO> skuInfos = validateAllSkusExist(request.getSkuIds());
        SkuListBO sku = skuInfos.stream()
                .filter(skuInfo -> skuInfo.getId().equals(mainSku.getSkuId()))
                .findFirst()
                .orElseThrow(() -> new BusinessException("主品不存在"));

        // 查询SPU信息（使用主品的SPU ID）
        SpuBO spuInfo = null;
        if (sku.getSpuId() != null) {
            spuInfo = spuService.getById(sku.getSpuId());
        }
        if (ObjectUtil.isNull(spuInfo)) {
            throw new BusinessException("主品SPU不存在，ID：" + sku.getSpuId());
        }
        // 转换为服务层命令对象，主品的SPU信息作为组合品的属性信息进行赋值
        String combinationCode = CodeGenerationUtil.generateComboCode();
        CombinationCreateCommand command = CombinationDTOConverter.toCreateCommand(request, user.getUserId(),
                spuInfo, combinationCode);

        command.setCombinationCode(combinationCode);
        //事务开始
        transactionTemplate.execute((TransactionCallback<Long>) status -> {
            // 调用服务层创建组合商品
            Long combinationId = combinationService.create(command);
            //创建泛微流程
            String requestId = createComboApprovalWorkflow(
                    command, user, skuInfos
            );
            //更新 oa 状态
            combinationService.updateOaStatus(
                    CombinationUpdateCommand.builder()
                            .id(combinationId)
                            .oaId(Long.valueOf(requestId))
                            .status(GoodsStatusEnum.APPROVING)
                            .build());
            return null;
        });
    }

    /**
     * 更新组合商品
     *
     * @param request 更新请求参数，不能为空
     * @throws IllegalArgumentException 当请求参数为空时抛出
     */
    public void update(@NotNull CombinationUpdateRequest request) {
        // 参数校验
        validateUpdateRequest(request);
        // 获取当前操作人ID
        Long operatorId = SecurityContextUtil.getCurrentUserIdLenient();
        // 检查组合商品是否存在
        CombinationDetailBO existingCombination = combinationService.getById(request.getId());
        if (existingCombination == null) {
            throw new IllegalArgumentException("组合商品不存在，ID：" + request.getId());
        }
        // 转换为服务层命令对象
        CombinationUpdateCommand command = CombinationDTOConverter.toUpdateCommand(request, operatorId);
        // 调用服务层更新组合商品
        combinationService.updateRelationInfo(command);
    }

    /**
     * 检查组合商品名称是否已存在
     *
     * @param name 商品名称
     * @param excludeId 排除的ID（用于更新时排除自身）
     * @return 是否存在
     */
    public Boolean existsByName(String name, Long excludeId) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        return combinationService.existsByName(name.trim(), excludeId);
    }


    /**
     * 安全获取SKU信息
     */
    private List<SkuListBO> getSkus(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        List<SkuListBO> result = skuService.list(SkuBOListQuery.builder()
                .ids(ids)
                .pager(new BasePager.Pager(1, ids.size())).build());
        return CollUtil.isEmpty(result) ? Collections.emptyList() : result;
    }

    /**
     * 校验更新组合商品请求参数
     *
     * @param request 更新请求
     */
    private void validateUpdateRequest(CombinationUpdateRequest request) {
        // 校验名称
        if (request.getCombinationName() != null && !request.getCombinationName().trim().isEmpty()) {
            // 检查名称是否重复（排除当前记录）
            if (existsByName(request.getCombinationName().trim(), request.getId())) {
                throw new IllegalArgumentException("组合商品名称已存在：" + request.getCombinationName().trim());
            }
        }

        // 校验SKU列表
        if (CollUtil.isNotEmpty(request.getSkuItems())) {
            // sku id 为空直接报错
            if (request.getSkuItems().stream().anyMatch(item -> ObjectUtil.isNull(item.getSkuId()))) {
                throw new IllegalArgumentException("SKU ID不能为空");
            }
            // 校验是否只有一个主品
            long mainCount = request.getSkuItems().stream()
                    .filter(CombinationSkuItem::getMain)
                    .count();
            if (mainCount > 1) {
                throw new IllegalArgumentException("组合商品只能有一个主品");
            }
        }
    }

    /**
     * 校验创建组合商品请求参数
     *
     * @param request 创建请求参数
     * @throws IllegalArgumentException 当参数不合法时抛出
     */
    private void validateCreateRequest(CombinationCreateRequest request) {
        // 校验组合商品名称是否已存在
        if (existsByName(request.getCombinationName().trim(), null)) {
            throw new IllegalArgumentException("组合商品名称已存在：" + request.getCombinationName().trim());
        }
        // 校验是否只有一个主品
        long mainSkuCount = request.getSkuIds().stream()
                .filter(CombinationSkuItem::getMain)
                .count();
        if (mainSkuCount != 1) {
            throw new IllegalArgumentException("组合商品只能有一个主品");
        }
    }

    /**
     * 校验所有SKU是否存在
     *
     * @param skuItems SKU项列表
     * @throws IllegalArgumentException 当有SKU不存在时抛出
     */
    private List<SkuListBO> validateAllSkusExist(List<CombinationSkuItem> skuItems) {
        // 提取所有SKU ID
        List<Long> skuIds = skuItems.stream()
                .map(CombinationSkuItem::getSkuId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(skuIds)) {
            throw new BusinessException("SKU ID列表不能为空");
        }

        // 批量查询SKU信息
        List<SkuListBO> existingSkus = skuService.list(SkuBOListQuery.builder()
                .ids(skuIds)
                .statuses(Collections.singletonList(GoodsStatusEnum.SUCCESS))
                .pager(new BasePager.Pager(1, skuIds.size()))
                .build());

        Set<Long> existingSkuIds = existingSkus.stream()
                .map(SkuListBO::getId)
                .collect(Collectors.toSet());

        // 检查是否有不存在的SKU
        List<Long> notExistSkuIds = skuIds.stream()
                .filter(id -> !existingSkuIds.contains(id))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(notExistSkuIds)) {
            throw new BusinessException("以下SKU不存在：" + notExistSkuIds);
        }
        return existingSkus;
    }


    private String createComboApprovalWorkflow(CombinationCreateCommand command, JwtUserDetails user,
                                               List<SkuListBO> skuInfos) {
        Map<String, Object> params = CombinationDTOConverter.convert2workflowRequest(command, user, skuInfos);
        String requestId = weaverWorkflowService.createWorkflowRequest(params, String.valueOf(user.getWeaverId()));
        if (StringUtils.isEmpty(requestId)) {
            throw new BusinessException("创建SKU立项审批工作流失败");
        } else {
            return requestId;
        }
    }

    /**
     * 填充组合品信息
     */
    private List<CombinationPageDTO> fillCombinationInfo(List<CombinationListBO> combinations) {
        if (CollUtil.isEmpty(combinations)) {
            return Collections.emptyList();
        }
        List<Long> skuIds =
                combinations.stream().flatMap(a -> Optional.ofNullable(a.getSkuIds()).orElse(Collections.emptyList()).stream())
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        // 并行获取关联数据以提升性能
        List<SkuListBO> skus = getSkus(skuIds);

        // 创建人信息
        List<Long> userIds = combinations.stream().map(CombinationListBO::getCreatorId).collect(Collectors.toList());
        List<UserBO> users = userService.getUserPage(UserPageQuery.builder().ids(userIds).build());
        //属性信息
        List<GoodsCategoryBO> categories = getCategories(combinations.stream().map(CombinationListBO::getCategoryId)
                .collect(Collectors.toList()));
        List<GoodsChannelBO> channels = getChannels(combinations.stream().map(CombinationListBO::getChannelId)
                .collect(Collectors.toList()));
        List<GoodsSeriesBO> series = getSeries(combinations.stream().map(CombinationListBO::getSeriesId)
                .collect(Collectors.toList()));
        //一二三四级类目
        List<GoodsClassificationBO> classifications = getClassifications(combinations.stream()
                .flatMap(a -> Stream.of(a.getFirstClassification(), a.getSecondClassification(),
                        a.getThirdClassification(), a.getFourthClassification()))
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList()));
        List<GoodsBrandBO> brands = getBrands(combinations.stream().map(CombinationListBO::getBrandId)
                .collect(Collectors.toList()));
        return CombinationDTOConverter.toListDTO(combinations, skus, categories, channels,
                series, classifications, users, brands);
    }

    /**
     * 获取类别信息
     */
    private List<GoodsClassificationBO> getClassifications(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return goodsClassificationService.getClassification(ids);
    }

    /**
     * 获取系列信息
     */
    private List<GoodsSeriesBO> getSeries(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return goodsSeriesService.list(SeriesBOPagerQuery.builder()
                .ids(ids)
                .pager(new BasePager.Pager(1, ids.size()))
                .build());
    }

    /**
     * 获取渠道信息
     */
    private List<GoodsChannelBO> getChannels(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return goodsChannelService.list(ChannelBOPagerQuery.builder()
                .ids(ids)
                .pager(new BasePager.Pager(1, ids.size()))
                .build());
    }

    /**
     * 获取类别信息
     */
    private List<GoodsCategoryBO> getCategories(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return goodsCategoryService.list(CategoryBOPagerQuery.builder()
                .ids(ids)
                .pager(new BasePager.Pager(1, ids.size()))
                .build());
    }

    /**
     * 获取品牌信息
     */
    private List<GoodsBrandBO> getBrands(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return goodsBrandService.listBrands(BrandBOPagerQuery.builder()
                .ids(ids)
                .pager(new BasePager.Pager(1, ids.size()))
                .build());
    }

    /**
     * 批量导入组合品
     *
     * @param file Excel文件
     * @return 导入结果
     * @throws IOException 文件读取异常
     */
    public CombinationBatchImportResultVO batchImport(MultipartFile file) throws IOException {
        log.info("开始批量导入组合品，文件名：{}", file.getOriginalFilename());

        // 1. 读取Excel文件
        List<CombinationBatchImportRequest> importDataList = readExcelFile(file);
        if (importDataList.isEmpty()) {
            return CombinationBatchImportConverter.buildEmptyResult();
        }

        // 2. 验证数据格式
        CombinationDataValidationResult validationResult = validateImportData(importDataList);

        // 3. 按组合品名称分组并验证业务规则
        Map<String, List<CombinationBatchImportRequest>> groupedData =
                validationResult.getValidDataList().stream()
                        .collect(Collectors.groupingBy(CombinationBatchImportRequest::getCombinationName));

        // 4. 验证业务规则并获取SKU信息
        CombinationBusinessValidationResult businessValidationResult = validateBusinessRules(groupedData,
                validationResult);

        // 5. 处理有效数据
        int successCount = processValidCombinations(businessValidationResult);

        // 6. 构建并返回结果
        return CombinationBatchImportConverter.buildImportResult(importDataList.size(), successCount,
                businessValidationResult.getFailDetails());
    }

    /**
     * 读取Excel文件
     */
    private List<CombinationBatchImportRequest> readExcelFile(MultipartFile file) throws IOException {
        List<CombinationBatchImportRequest> importDataList = new ArrayList<>();

        EasyExcel.read(file.getInputStream(), CombinationBatchImportRequest.class,
                new AnalysisEventListener<CombinationBatchImportRequest>() {
                    @Override
                    public void invoke(CombinationBatchImportRequest data, AnalysisContext context) {
                        importDataList.add(data);
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                        log.info("Excel文件读取完成，共读取{}条数据", importDataList.size());
                    }
                }).sheet().doRead();

        return importDataList;
    }


    /**
     * 验证导入数据格式
     */
    private CombinationDataValidationResult validateImportData(List<CombinationBatchImportRequest> importDataList) {
        List<CombinationBatchImportRequest> validDataList = new ArrayList<>();
        List<CombinationBatchImportResultVO.CombinationImportFailDetail> failDetails = new ArrayList<>();

        for (int i = 0; i < importDataList.size(); i++) {
            CombinationBatchImportRequest importData = importDataList.get(i);
            int rowNum = i + 2; // Excel行号从2开始（第1行是表头）

            String validateError = CombinationBatchImportConverter.validateImportData(importData);
            if (validateError != null) {
                failDetails.add(CombinationBatchImportConverter.createFailDetail(rowNum,
                        importData.getCombinationName(), validateError));
            } else {
                validDataList.add(importData);
            }
        }

        return CombinationDataValidationResult.builder()
                .validDataList(validDataList)
                .failDetails(failDetails)
                .build();
    }

    /**
     * 验证业务规则
     */
    private CombinationBusinessValidationResult validateBusinessRules(
            Map<String, List<CombinationBatchImportRequest>> groupedData,
            CombinationDataValidationResult validationResult) {

        List<CombinationBatchImportResultVO.CombinationImportFailDetail> failDetails =
                new ArrayList<>(validationResult.getFailDetails());
        Map<String, List<CombinationBatchImportRequest>> validCombinations = new HashMap<>();
        Map<String, Long> skuCodeToIdMap = new HashMap<>();

        // 获取所有SKU编码
        Set<String> allSkuCodes = groupedData.values().stream()
                .flatMap(List::stream)
                .map(CombinationBatchImportRequest::getSkuCode)
                .collect(Collectors.toSet());

        // 批量查询SKU信息
        List<SkuListBO> skuList = skuService.list(SkuBOListQuery.builder()
                .skuCodes(new ArrayList<>(allSkuCodes))
                .statuses(Collections.singletonList(GoodsStatusEnum.SUCCESS))
                .pager(new BasePager.Pager(1, allSkuCodes.size()))
                .build());

        // 构建SKU编码到ID的映射
        skuList.forEach(sku -> skuCodeToIdMap.put(sku.getSkuCode(), sku.getId()));

        // 验证每个组合品
        for (Map.Entry<String, List<CombinationBatchImportRequest>> entry : groupedData.entrySet()) {
            String combinationName = entry.getKey();
            List<CombinationBatchImportRequest> requests = entry.getValue();

            // 验证组合品业务规则
            String businessError = CombinationBatchImportConverter.validateCombinationBusinessRules(
                    combinationName, requests);
            if (businessError != null) {
                // 为该组合品的所有行添加失败记录
                requests.forEach(request -> {
                    int rowNum = validationResult.getValidDataList().indexOf(request) + 2;
                    failDetails.add(CombinationBatchImportConverter.createFailDetail(rowNum, combinationName,
                            businessError));
                });
                continue;
            }

            // 验证SKU是否存在
            List<String> notExistSkuCodes = requests.stream()
                    .map(CombinationBatchImportRequest::getSkuCode)
                    .filter(code -> !skuCodeToIdMap.containsKey(code))
                    .collect(Collectors.toList());

            if (!notExistSkuCodes.isEmpty()) {
                String error = "以下SKU编码不存在：" + String.join(",", notExistSkuCodes);
                requests.forEach(request -> {
                    int rowNum = validationResult.getValidDataList().indexOf(request) + 2;
                    failDetails.add(CombinationBatchImportConverter.createFailDetail(rowNum, combinationName, error));
                });
                continue;
            }

            // 验证SKU编码和名称的一致性
            String consistencyError = CombinationBatchImportConverter.validateSkuCodeNameConsistency(
                    combinationName, requests, skuList);
            if (consistencyError != null) {
                requests.forEach(request -> {
                    int rowNum = validationResult.getValidDataList().indexOf(request) + 2;
                    failDetails.add(CombinationBatchImportConverter.createFailDetail(rowNum, combinationName,
                            consistencyError));
                });
                continue;
            }

            // 验证组合品名称是否已存在
            if (existsByName(combinationName.trim(), null)) {
                String error = "组合品名称已存在：" + combinationName;
                requests.forEach(request -> {
                    int rowNum = validationResult.getValidDataList().indexOf(request) + 2;
                    failDetails.add(CombinationBatchImportConverter.createFailDetail(rowNum, combinationName, error));
                });
                continue;
            }

            validCombinations.put(combinationName, requests);
        }
        return CombinationBusinessValidationResult.builder()
                .validCombinations(validCombinations)
                .skuCodeToIdMap(skuCodeToIdMap)
                .failDetails(failDetails)
                .build();
    }

    /**
     * 处理有效的组合品数据
     */
    private int processValidCombinations(CombinationBusinessValidationResult businessValidationResult) {
        int successCount = 0;
        JwtUserDetails user = SecurityContextUtil.getCurrentUserDetails().get();

        for (Map.Entry<String, List<CombinationBatchImportRequest>> entry :
                businessValidationResult.getValidCombinations().entrySet()) {

            String combinationName = entry.getKey();
            List<CombinationBatchImportRequest> requests = entry.getValue();

            try {
                // 转换为创建请求
                CombinationCreateRequest createRequest = CombinationBatchImportConverter.toCreateRequest(
                        combinationName, requests, businessValidationResult.getSkuCodeToIdMap());

                // 创建组合品
                create(createRequest);
                successCount++;
                log.info("成功创建组合品：{}", combinationName);
            } catch (Exception e) {
                log.error("创建组合品失败：{}", combinationName, e);
                // 这里可以添加失败记录，但由于已经过了验证阶段，一般不会失败
            }
        }

        return successCount;
    }


    /**
     * 下载组合品导入模板
     *
     * @param response HTTP响应
     * @throws IOException 文件写入异常
     */
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        log.info("开始下载组合品导入模板");

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("组合品导入模板", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        // 创建模板数据
        List<CombinationBatchImportRequest> templateData = CombinationBatchImportConverter.createTemplateData();

        // 写入Excel
        EasyExcel.write(response.getOutputStream(), CombinationBatchImportRequest.class)
                .sheet("组合品导入模板")
                .doWrite(templateData);

        log.info("组合品导入模板下载完成");
    }




}
