package com.spes.sop.support.goods.model.response;

import com.spes.sop.controller.goods.model.request.CombinationBatchImportRequest;
import com.spes.sop.controller.goods.model.vo.CombinationBatchImportResultVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 组合品数据验证结果
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationDataValidationResult {

    /**
     * 有效的数据列表
     */
    private List<CombinationBatchImportRequest> validDataList;

    /**
     * 失败详情列表
     */
    private List<CombinationBatchImportResultVO.CombinationImportFailDetail> failDetails;
} 