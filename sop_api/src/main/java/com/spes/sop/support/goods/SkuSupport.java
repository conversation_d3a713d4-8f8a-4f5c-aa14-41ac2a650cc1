package com.spes.sop.support.goods;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.aliyuncs.utils.StringUtils;
import com.spes.sop.common.enums.GoodsStatusEnum;
import com.spes.sop.common.exception.BusinessException;
import com.spes.sop.common.page.BasePager;
import com.spes.sop.common.page.PageResult;
import com.spes.sop.common.security.JwtUserDetails;
import com.spes.sop.common.security.SecurityContextUtil;
import com.spes.sop.common.util.CodeGenerationUtil;
import com.spes.sop.controller.goods.model.query.SkuPageQuery;
import com.spes.sop.controller.goods.model.request.SkuApprovalRequest;
import com.spes.sop.controller.goods.model.request.SkuBatchImportRequest;
import com.spes.sop.controller.goods.model.request.SkuCreateRequest;
import com.spes.sop.controller.goods.model.request.SkuUpdateRequest;
import com.spes.sop.controller.goods.model.vo.SkuBatchImportResultVO;
import com.spes.sop.goods.service.sku.SkuService;
import com.spes.sop.goods.service.sku.model.bo.SkuDetailBO;
import com.spes.sop.goods.service.sku.model.bo.SkuListBO;
import com.spes.sop.goods.service.sku.model.bo.value.SkuBusinessInfoValueBO;
import com.spes.sop.goods.service.sku.model.command.SkuCreateCommand;
import com.spes.sop.goods.service.sku.model.command.SkuUpdateCommand;
import com.spes.sop.goods.service.sku.model.query.SkuBOListQuery;
import com.spes.sop.support.goods.converter.SkuBatchImportConverter;
import com.spes.sop.support.goods.converter.SkuDTOConverter;
import com.spes.sop.support.goods.model.dto.SkuDetailDTO;
import com.spes.sop.support.goods.model.dto.SkuListDTO;
import com.spes.sop.support.goods.model.response.DataValidationResult;
import com.spes.sop.third.weaver.service.WeaverWorkflowService;
import com.spes.sop.user.service.user.UserService;
import com.spes.sop.user.service.user.model.bo.UserBO;
import com.spes.sop.user.service.user.model.query.UserGetQuery;
import com.spes.sop.user.service.user.model.query.UserPageQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * SKU支持类，提供SKU相关的业务逻辑支持
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SkuSupport {

    private final SkuService skuService;
    private final TransactionTemplate transactionTemplate;
    private final UserService userService;
    private final WeaverWorkflowService weaverWorkflowService;

    /**
     * 分页查询SKU列表
     *
     * @param request 分页查询请求参数，不能为空
     * @return 分页结果，包含SKU列表和分页信息
     * @throws IllegalArgumentException 当请求参数为空时抛出
     */
    public PageResult<SkuListDTO> page(@NotNull SkuPageQuery request) {
        log.debug("开始分页查询SKU，页码：{}，页大小：{}", request.getPageNum(), request.getPageSize());

        JwtUserDetails userDetails = SecurityContextUtil.getCurrentUserDetails().get();

        SkuBOListQuery query = SkuDTOConverter.toServiceQuery(request);
        Long count = skuService.count(query);
        if (ObjectUtil.isNull(count) || count <= 0) {
            return PageResult.of(Collections.emptyList(), 0L, request.getPageNum(), request.getPageSize());
        }
        List<SkuListDTO> data = getSkus(query, userDetails);

        return PageResult.of(data, count, request.getPageNum(), request.getPageSize());
    }

    /**
     * 根据 spu 获取关联的 sku 信息
     */
    public List<SkuListDTO> getRelationSkus(List<Long> spuIds) {
        if (CollectionUtils.isEmpty(spuIds)) {
            return Collections.emptyList();
        }
        JwtUserDetails userDetails = SecurityContextUtil.getCurrentUserDetails().get();
        SkuBOListQuery query = SkuBOListQuery.builder()
                .spuIds(spuIds)
                .pager(new BasePager.Pager(1, Integer.MAX_VALUE))
                .build();
        return getSkus(query, userDetails);
    }



    /**
     * 根据ID获取SKU详情
     *
     * @param id SKU ID，不能为空
     * @return SKU详情，如果不存在则返回null
     * @throws IllegalArgumentException 当ID为空时抛出
     */
    public SkuDetailDTO getById(@NotNull Long id) {
        Objects.requireNonNull(id, "SKU ID不能为空");

        log.debug("开始获取SKU详情，ID：{}", id);

        JwtUserDetails userDetails = SecurityContextUtil.getCurrentUserDetails().get();

        SkuDetailBO sku = skuService.getById(id);
        if (sku == null) {
            log.debug("SKU不存在，ID：{}", id);
            return null;
        }
        UserBO creator = userService.getUser(UserGetQuery.builder().id(sku.getCreatorId()).build());
        UserBO updater = userService.getUser(UserGetQuery.builder().id(sku.getUpdaterId()).build());

        // 转换为DTO（使用完整版本，包含关联信息查询）
        SkuDetailDTO result = SkuDTOConverter.convertToSkuDetail(sku, userDetails);
        if (ObjectUtil.isNotNull(creator)) {
            result.setCreatorName(creator.getUsername());
        }
        if (ObjectUtil.isNotNull(updater)) {
            result.setUpdaterName(updater.getUsername());
        }
        log.debug("获取SKU详情完成，ID：{}", id);
        return result;
    }

    /**
     * 创建SKU
     *
     * @param request 创建请求参数，不能为空
     * @throws IllegalArgumentException 当请求参数为空时抛出
     */
    public void create(@Valid SkuCreateRequest request) {
        Objects.requireNonNull(request, "创建SKU请求参数不能为空");
        log.info("开始创建SKU，SKU名称：{}", request.getSkuName());
        // 从上下文获取当前用户ID
        Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();
        // 生成SKU编码
        String skuCode = CodeGenerationUtil.generateSkuCode(request.getSpecification());
        // 创建SKU创建命令
        SkuCreateCommand command = SkuDTOConverter.buildSkuCreateCommand(request, currentUserId, skuCode);
        // 执行创建逻辑
        skuService.create(command);
    }

    /**
     * 删除SKU
     *
     * @param id SKU ID，不能为空
     * @throws IllegalArgumentException 当ID为空时抛出
     */
    public void delete(@NotNull Long id) {
        Objects.requireNonNull(id, "SKU ID不能为空");
        log.info("开始删除SKU，ID：{}", id);

        // 使用事务模板确保数据一致性
        transactionTemplate.execute(status -> {
            // 检查SKU是否存在
            SkuDetailBO existingSku = skuService.getById(id);
            if (existingSku == null) {
                throw new IllegalArgumentException("SKU不存在，ID：" + id);
            }

            // 从上下文获取当前用户ID
            Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();

            // 执行删除逻辑（逻辑删除）
            skuService.delete(id, currentUserId);

            log.info("删除SKU成功，ID：{}", id);
            return null;
        });
    }

    /**
     * 更新SKU
     *
     * @param request 更新请求参数，不能为空
     * @throws IllegalArgumentException 当请求参数为空时抛出
     */
    public void update(@Valid SkuUpdateRequest request) {
        Objects.requireNonNull(request, "更新SKU请求参数不能为空");
        Objects.requireNonNull(request.getId(), "SKU ID不能为空");
        log.info("开始更新SKU，ID：{}，名称：{}", request.getId(), request.getSkuName());

        // 使用事务模板确保数据一致性
        transactionTemplate.execute(status -> {
            // 检查SKU是否存在
            SkuDetailBO existingSku = skuService.getById(request.getId());
            if (existingSku == null) {
                throw new BusinessException("SKU不存在，ID：" + request.getId());
            }

            // 转换为更新请求对象
            SkuUpdateCommand updateReq = SkuDTOConverter.toCmd(request);
            if (updateReq == null) {
                throw new BusinessException("转换更新请求失败");
            }

            // 从上下文获取当前用户ID
            Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();
            updateReq.setUpdaterId(currentUserId);
            // 执行更新逻辑
            skuService.update(updateReq);
            log.info("更新SKU成功，ID：{}，名称：{}", request.getId(), request.getSkuName());
            return null;
        });
    }

    /**
     * 发起SKU审批
     *
     * @param request 审批请求参数，不能为空
     */
    public void approve(@Valid SkuApprovalRequest request) {
        Objects.requireNonNull(request, "SKU审批请求参数不能为空");
        JwtUserDetails user = SecurityContextUtil.getCurrentUserDetails().get();
        Objects.requireNonNull(user, "当前用户信息不能为空");
        // 批量获取所有SKU详细信息
        List<SkuListBO> skuList = batchGetSkuDetails(request.getIds());
        // 使用事务模板确保数据一致性
        transactionTemplate.execute(status -> {
            // 调用泛微工作流服务创建审批流程
            String requestId = createSkuApprovalWorkflow(
                    skuList,
                    request.getAttachments(),
                    user
            );
            // 更新SKU状态为"审批中"并保存工作流ID
            updateSkuApprovalStatus(skuList, requestId, user.getUserId(), request.getAttachments());
            return null;
        });
    }

    private List<SkuListDTO> getSkus(SkuBOListQuery query, JwtUserDetails userDetails) {
        List<SkuListBO> skus = skuService.list(query);
        if (CollectionUtils.isEmpty(skus)) {
            return Collections.emptyList();
        }
        //  更新人、创建人信息
        List<Long> userIds = skus.stream().map(SkuListBO::getCreatorId).collect(Collectors.toList());
        userIds.addAll(skus.stream().map(SkuListBO::getUpdaterId).collect(Collectors.toList()));
        List<UserBO> users = userService.getUserPage(UserPageQuery.builder().ids(userIds).build());
        // 转换为DTO
        return SkuDTOConverter.convertToSkuList(skus, users, userDetails);
    }

    /**
     * 批量获取SKU详细信息
     *
     * @param skuIds SKU ID列表
     * @return SKU详细信息列表
     * @throws IllegalArgumentException 当有SKU不存在时抛出
     */
    private List<SkuListBO> batchGetSkuDetails(List<Long> skuIds) {
        if (skuIds == null || skuIds.isEmpty()) {
            throw new IllegalArgumentException("SKU ID列表不能为空");
        }
        // 构建批量查询条件
        SkuBOListQuery query = SkuBOListQuery.builder()
                .ids(skuIds)
                .statuses(Collections.singletonList(GoodsStatusEnum.CREATED))
                .build();

        // 批量查询SKU列表
        List<SkuListBO> skuListBOs = skuService.list(query);
        if (skuListBOs == null || skuListBOs.isEmpty()) {
            throw new BusinessException("未找到任何SKU，请检查ID列表：" + skuIds);
        }
        // 检查是否有缺失的SKU
        Set<Long> foundSkuIds = skuListBOs.stream()
                .map(SkuListBO::getId)
                .collect(Collectors.toSet());

        List<Long> missingSkuIds = skuIds.stream()
                .filter(id -> !foundSkuIds.contains(id))
                .collect(Collectors.toList());

        if (!missingSkuIds.isEmpty()) {
            throw new IllegalArgumentException("以下SKU不存在或状态不正确：" + missingSkuIds);
        }
        if (skuListBOs.size() != skuIds.size()) {
            log.warn("批量查询SKU详细信息部分失败，期望：{}，实际：{}", skuIds.size(), skuListBOs.size());
        }

        return skuListBOs;
    }

    /**
     * 更新SKU审批状态
     *
     * @param skuList           SKU列表
     * @param workflowRequestId 泛微工作流请求ID
     * @param operatorId        操作人ID
     */
    private void updateSkuApprovalStatus(List<SkuListBO> skuList, String workflowRequestId, Long operatorId,
                                         List<String> attachments) {
        for (SkuListBO sku : skuList) {
            SkuBusinessInfoValueBO businessInfo = sku.getBusinessInfo();
            businessInfo.setAttachments(attachments);
            SkuUpdateCommand updateReq = SkuUpdateCommand.builder()
                    .id(sku.getId())
                    .status(GoodsStatusEnum.APPROVING) // 设置为审批中状态
                    .oaId(Long.valueOf(workflowRequestId)) // 保存泛微工作流ID
                    .updaterId(operatorId)
                    .businessInfo(businessInfo)
                    .build();
            skuService.update(updateReq);
        }
    }


    /**
     * 创建SKU立项审批工作流
     *
     * @param skuList     SKU列表
     * @param attachments 附件列表
     * @return 工作流请求ID
     * @throws BusinessException 创建失败时抛出
     */
    public String createSkuApprovalWorkflow(List<SkuListBO> skuList, List<String> attachments,
                                            JwtUserDetails user) {
        Map<String, Object> params = SkuDTOConverter.convert2workflowRequest(skuList, attachments, user);
        String requestId = weaverWorkflowService.createWorkflowRequest(params, String.valueOf(user.getWeaverId()));
        if (StringUtils.isEmpty(requestId)) {
            throw new BusinessException("创建SKU立项审批工作流失败");
        } else {
            return requestId;
        }
    }

    /**
     * 批量导入SKU
     *
     * @param file Excel文件
     * @return 导入结果
     * @throws IOException 文件读取异常
     */
    public SkuBatchImportResultVO batchImport(MultipartFile file) throws IOException {
        log.info("开始批量导入SKU，文件名：{}", file.getOriginalFilename());

        // 1. 读取Excel文件
        List<SkuBatchImportRequest> importDataList = readExcelFile(file);
        if (importDataList.isEmpty()) {
            return SkuBatchImportConverter.buildEmptyResult();
        }

        // 2. 验证数据
        DataValidationResult validationResult = validateImportData(importDataList);

        // 3. 处理有效数据
        int successCount = processValidData(validationResult);

        // 4. 处理失败的有效数据（如果批量处理失败）
        handleBatchProcessFailure(validationResult, successCount);

        // 5. 构建并返回结果
        return SkuBatchImportConverter.buildImportResult(importDataList.size() - 1, successCount,
                validationResult.getFailDetails());
    }


    /**
     * 读取Excel文件
     */
    private List<SkuBatchImportRequest> readExcelFile(MultipartFile file) throws IOException {
        List<SkuBatchImportRequest> importDataList = new ArrayList<>();

        EasyExcel.read(file.getInputStream(), SkuBatchImportRequest.class,
                new AnalysisEventListener<SkuBatchImportRequest>() {
                    @Override
                    public void invoke(SkuBatchImportRequest data, AnalysisContext context) {
                        importDataList.add(data);
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                        log.info("Excel文件读取完成，共读取{}条数据", importDataList.size());
                    }
                }).sheet().doRead();

        return importDataList;
    }



    /**
     * 验证导入数据
     */
    private DataValidationResult validateImportData(List<SkuBatchImportRequest> importDataList) {
        List<SkuBatchImportRequest> validDataList = new ArrayList<>();
        List<Integer> validRowNums = new ArrayList<>();
        List<SkuBatchImportResultVO.SkuImportFailDetail> failDetails = new ArrayList<>();

        for (int i = 1; i < importDataList.size(); i++) {
            SkuBatchImportRequest importData = importDataList.get(i);
            int rowNum = i + 3; // Excel行号从3开始（第1行是表头, 第2行示例不能动）

            String validateError = SkuBatchImportConverter.validateImportData(importData);
            if (validateError != null) {
                failDetails.add(SkuBatchImportConverter.createFailDetail(rowNum, importData.getSkuName(),
                        validateError));
            } else {
                validDataList.add(importData);
                validRowNums.add(rowNum);
            }
        }

        return new DataValidationResult(validDataList, validRowNums, failDetails);
    }

    /**
     * 处理有效数据
     */
    private int processValidData(DataValidationResult validationResult) {
        if (CollUtil.isEmpty(validationResult.getValidDataList())) {
            return 0;
        }
        try {
            return processBatchImport(validationResult.getValidDataList());
        } catch (Exception e) {
            log.error("批量导入SKU失败", e);
            throw e; // 重新抛出异常，由调用方处理
        }
    }

    /**
     * 处理批量处理失败的情况
     */
    private void handleBatchProcessFailure(DataValidationResult validationResult, int successCount) {
        if (successCount == 0 && CollUtil.isNotEmpty(validationResult.getValidDataList())) {
            // 批量处理失败，将所有有效数据标记为失败
            for (int i = 0; i < validationResult.getValidDataList().size(); i++) {
                validationResult.getFailDetails().add(SkuBatchImportConverter.createFailDetail(
                        validationResult.getValidRowNums().get(i),
                        validationResult.getValidDataList().get(i).getSkuName(),
                        "批量处理失败"
                ));
            }
        }
    }



    /**
     * 处理批量导入（重构后的核心逻辑）
     */
    private int processBatchImport(List<SkuBatchImportRequest> validDataList) {
        validateBatchImportPreconditions(validDataList);
        Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();
        List<Long> specCodes = extractSpecificationCodes(validDataList);
        List<String> skuCodes = generateUniqueSkuCodes(specCodes);
        List<SkuCreateCommand> createCommands = convertToCreateCommands(validDataList, currentUserId, skuCodes);
        return executeBatchCreate(createCommands);
    }

    /**
     * 验证批量导入前置条件
     */
    private void validateBatchImportPreconditions(List<SkuBatchImportRequest> validDataList) {
        Objects.requireNonNull(transactionTemplate, "事务模板不能为空");
        Objects.requireNonNull(skuService, "SKU服务不能为空");
        Objects.requireNonNull(validDataList, "有效数据列表不能为空");

        if (validDataList.isEmpty()) {
            throw new IllegalArgumentException("有效数据列表为空，无需处理");
        }
    }

    /**
     * 提取规格代号列表
     */
    private List<Long> extractSpecificationCodes(List<SkuBatchImportRequest> validDataList) {
        List<Long> specCodes = validDataList.stream()
                .filter(Objects::nonNull)
                .map(SkuBatchImportRequest::getSpecification)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (specCodes.isEmpty()) {
            throw new IllegalArgumentException("规格代号列表为空，无法生成SKU编码");
        }

        return specCodes;
    }

    /**
     * 生成唯一SKU编码
     */
    private List<String> generateUniqueSkuCodes(List<Long> specCodes) {
        List<String> skuCodes = CodeGenerationUtil.generateUniqueSkuCodes(specCodes);
        Objects.requireNonNull(skuCodes, "生成的SKU编码列表不能为空");

        if (skuCodes.size() != specCodes.size()) {
            throw new IllegalStateException("生成的SKU编码数量与规格代号数量不匹配");
        }

        return skuCodes;
    }

    /**
     * 转换为创建命令列表
     */
    private List<SkuCreateCommand> convertToCreateCommands(List<SkuBatchImportRequest> validDataList,
                                                           Long currentUserId, List<String> skuCodes) {
        List<SkuCreateCommand> createCommands = new ArrayList<>();
        int codeIndex = 0;

        for (int i = 0; i < validDataList.size(); i++) {
            SkuBatchImportRequest importData = validDataList.get(i);
            if (importData == null || importData.getSpecification() == null) {
                continue;
            }

            SkuCreateRequest createRequest = SkuBatchImportConverter.toCreateRequest(importData);
            if (createRequest == null) {
                log.warn("转换创建请求失败，跳过第{}行数据", i + 1);
                continue;
            }

            SkuCreateCommand command = SkuDTOConverter.buildSkuCreateCommand(
                    createRequest, currentUserId, skuCodes.get(codeIndex++));
            createCommands.add(command);
        }

        if (createCommands.isEmpty()) {
            throw new IllegalArgumentException("没有有效的创建命令，无法执行批量创建");
        }

        return createCommands;
    }

    /**
     * 执行批量创建
     */
    private int executeBatchCreate(List<SkuCreateCommand> createCommands) {
        try {
            List<Long> createdIds = skuService.batchCreate(createCommands);
            int createdCount = CollUtil.isEmpty(createdIds) ? 0 : createdIds.size();
            log.info("批量创建SKU成功，创建数量：{}", createdCount);
            return createdCount;
        } catch (Exception e) {
            log.error("批量创建SKU失败", e);
            throw new RuntimeException("批量创建SKU失败：" + e.getMessage(), e);
        }
    }

    /**
     * 下载SKU导入模板
     *
     * @param response HTTP响应
     * @throws IOException 文件写入异常
     */
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        log.info("开始下载SKU导入模板");

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("SKU导入模板", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        // 创建模板数据
        List<SkuBatchImportRequest> templateData = SkuBatchImportConverter.createTemplateData();

        // 写入Excel
        EasyExcel.write(response.getOutputStream(), SkuBatchImportRequest.class)
                .sheet("SKU导入模板")
                .doWrite(templateData);

        log.info("SKU导入模板下载完成");
    }



}
