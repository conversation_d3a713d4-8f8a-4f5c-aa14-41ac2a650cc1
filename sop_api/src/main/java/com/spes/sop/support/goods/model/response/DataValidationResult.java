package com.spes.sop.support.goods.model.response;

import com.spes.sop.controller.goods.model.request.SkuBatchImportRequest;
import com.spes.sop.controller.goods.model.vo.SkuBatchImportResultVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataValidationResult {

    private List<SkuBatchImportRequest> validDataList;

    private List<Integer> validRowNums;

    private List<SkuBatchImportResultVO.SkuImportFailDetail> failDetails;
}
