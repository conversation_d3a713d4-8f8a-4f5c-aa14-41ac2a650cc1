package com.spes.sop.support.goods.converter;

import cn.hutool.core.util.StrUtil;
import com.spes.sop.controller.goods.model.request.SkuBatchImportRequest;
import com.spes.sop.controller.goods.model.request.SkuCreateRequest;
import com.spes.sop.controller.goods.model.vo.SkuBatchImportResultVO;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * SKU批量导入转换器
 *
 * <AUTHOR>
 */
public class SkuBatchImportConverter {

    /**
     * 将批量导入请求转换为创建请求
     *
     * @param importRequest 批量导入请求
     * @return 创建请求
     */
    public static SkuCreateRequest toCreateRequest(SkuBatchImportRequest importRequest) {
        if (importRequest == null) {
            return null;
        }

        return SkuCreateRequest.builder()
                .skuName(importRequest.getSkuName())
                .specification(importRequest.getSpecification())
                .applicationDate(importRequest.getApplicationDate())
                .launchDate(importRequest.getLaunchDate())
                .channelExclusive(importRequest.getChannelExclusive())
                .productPurpose(importRequest.getProductPurpose())
                .estimatedPrice(importRequest.getEstimatedPrice())
                .firstBatchQuantity(importRequest.getFirstBatchQuantity())
                .factoryMinOrderQuantity(importRequest.getFactoryMinOrderQuantity())
                .minOrderQuantity(importRequest.getMinOrderQuantity())
                .minOrderReason(importRequest.getMinOrderReason())
                .shelfLifeRequirement(importRequest.getShelfLifeRequirement())
                .exclusiveSale(importRequest.getExclusiveSale())
                .salesResponsibleEmployeeId(importRequest.getSalesResponsibleEmployeeId())
                .remark(importRequest.getRemark())
                .build();
    }

    /**
     * 验证导入数据
     *
     * @param importRequest 导入请求
     * @return 验证错误信息，如果为空则验证通过
     */
    public static String validateImportData(SkuBatchImportRequest importRequest) {
        if (importRequest == null) {
            return "导入数据不能为空";
        }
        //  除了渠道特供品和包销，其他字段都不能为空
        if (StrUtil.isBlank(importRequest.getSkuName())) {
            return "SKU名称不能为空";
        }
        if (importRequest.getSkuName().length() > 100) {
            return "SKU名称长度不能超过100个字符";
        }
        if (importRequest.getSpecification() == null) {
            return "货品规格不能为空";
        }
        if (importRequest.getApplicationDate() == null) {
            return "立项申请时间不能为空";
        }
        if (importRequest.getLaunchDate() == null) {
            return "产品上线时间不能为空";
        }
        if (importRequest.getEstimatedPrice() == null) {
            return "预估售价不能为空";
        }
        if (importRequest.getFirstBatchQuantity() == null) {
            return "首批需求数量不能为空";
        }
        if (importRequest.getFactoryMinOrderQuantity() == null) {
            return "工厂起订量不能为空";
        }
        if (importRequest.getMinOrderQuantity() == null) {
            return "最少起订量不能为空";
        }
        if (importRequest.getShelfLifeRequirement() == null) {
            return "效期要求不能为空";
        }
        if (importRequest.getSalesResponsibleEmployeeId() == null) {
            return "销售负责人不能为空";
        }
        //涉及到数量和金额都不能小于 0
        if (importRequest.getEstimatedPrice().compareTo(new java.math.BigDecimal("0")) < 0) {
            return "预估售价不能小于0";
        }
        if (importRequest.getFirstBatchQuantity() < 0) {
            return "首批需求数量不能小于0";
        }
        if (importRequest.getFactoryMinOrderQuantity() < 0) {
            return "工厂起订量不能小于0";
        }
        if (importRequest.getMinOrderQuantity() < 0) {
            return "最少起订量不能小于0";
        }
        return null;
    }

    /**
     * 创建导入结果
     *
     * @param totalCount   总条数
     * @param successCount 成功条数
     * @param failDetails  失败详情列表
     * @return 导入结果
     */
    public static SkuBatchImportResultVO buildImportResult(int totalCount, int successCount,
                                                           List<SkuBatchImportResultVO.SkuImportFailDetail> failDetails) {
        return SkuBatchImportResultVO.builder()
                .totalCount(totalCount)
                .successCount(successCount)
                .failCount(failDetails.size())
                .failDetails(failDetails)
                .build();
    }

    /**
     * 创建空的导入结果
     *
     * @return 空的导入结果
     */
    public static SkuBatchImportResultVO buildEmptyResult() {
        return SkuBatchImportResultVO.builder()
                .totalCount(0)
                .successCount(0)
                .failCount(0)
                .failDetails(new ArrayList<>())
                .build();
    }

    /**
     * 创建失败详情
     *
     * @param rowNum     行号
     * @param skuName    SKU名称
     * @param failReason 失败原因
     * @return 失败详情
     */
    public static SkuBatchImportResultVO.SkuImportFailDetail createFailDetail(
            int rowNum, String skuName, String failReason) {
        return SkuBatchImportResultVO.SkuImportFailDetail.builder()
                .rowNum(rowNum)
                .skuName(skuName)
                .failReason(failReason)
                .build();
    }

    /**
     * 创建模板数据
     *
     * @return 模板数据列表
     */
    public static List<SkuBatchImportRequest> createTemplateData() {
        List<SkuBatchImportRequest> templateData = new ArrayList<>();

        // 添加一行示例数据
        SkuBatchImportRequest example = SkuBatchImportRequest.builder()
                .skuName("示例SKU名称")
                .specification(1L)
                .applicationDate(new Date())
                .launchDate(new Date())
                .channelExclusiveStr("是/否")
                .productPurpose("示例产品用途")
                .estimatedPrice(new BigDecimal("100.09"))
                .firstBatchQuantity(1000)
                .factoryMinOrderQuantity(500)
                .minOrderQuantity(100)
                .minOrderReason("示例起订原因")
                .shelfLifeRequirement("24个月")
                .exclusiveSaleStr("是/否")
                .salesResponsibleEmployeeId("示例销售负责人")
                .remark("示例备注")
                .tips("日期请按'yyyy-MM-dd'填写，选项使用'是'或'否'，本行示例勿动")
                .build();

        templateData.add(example);
        return templateData;
    }
} 