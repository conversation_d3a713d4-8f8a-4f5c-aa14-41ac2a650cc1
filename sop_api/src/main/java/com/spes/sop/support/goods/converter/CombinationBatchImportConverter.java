package com.spes.sop.support.goods.converter;

import cn.hutool.core.util.StrUtil;
import com.spes.sop.controller.goods.model.request.CombinationBatchImportRequest;
import com.spes.sop.controller.goods.model.request.CombinationCreateRequest;
import com.spes.sop.controller.goods.model.request.value.CombinationSkuItem;
import com.spes.sop.goods.service.sku.model.bo.SkuListBO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 组合品批量导入转换器
 *
 * <AUTHOR>
 */
public class CombinationBatchImportConverter {

    /**
     * 将批量导入请求转换为创建请求
     *
     * @param combinationName 组合品名称
     * @param importRequests  同一组合品的导入请求列表
     * @param skuCodeToIdMap  SKU编码到ID的映射
     * @return 创建请求
     */
    public static CombinationCreateRequest toCreateRequest(String combinationName,
                                                           List<CombinationBatchImportRequest> importRequests,
                                                           Map<String, Long> skuCodeToIdMap) {
        if (importRequests == null || importRequests.isEmpty()) {
            return null;
        }

        // 取第一行的描述信息
        String description = importRequests.get(0).getDescription();

        // 转换 SKU 项
        List<CombinationSkuItem> skuItems = importRequests.stream()
                .map(request -> {
                    Long skuId = skuCodeToIdMap.get(request.getSkuCode());
                    return CombinationSkuItem.builder()
                            .skuId(skuId)
                            .skuNum(request.getSkuNum())
                            .main(request.getMain())
                            .gift(request.getGift())
                            .build();
                })
                .collect(Collectors.toList());

        return CombinationCreateRequest.builder()
                .combinationName(combinationName)
                .description(description)
                .skuIds(skuItems)
                .build();
    }

    /**
     * 验证导入数据
     *
     * @param importRequest 导入请求
     * @return 验证错误信息，如果为空则验证通过
     */
    public static String validateImportData(CombinationBatchImportRequest importRequest) {
        if (importRequest == null) {
            return "导入数据不能为空";
        }

        // 验证组合品名称
        if (StrUtil.isBlank(importRequest.getCombinationName())) {
            return "组合品名称不能为空";
        }

        // 验证 SKU 编码
        if (StrUtil.isBlank(importRequest.getSkuCode())) {
            return "SKU编码不能为空";
        }

        // 验证 SKU 名称
        if (StrUtil.isBlank(importRequest.getSkuName())) {
            return "SKU名称不能为空";
        }

        // 验证 SKU 数量
        if (importRequest.getSkuNum() == null) {
            return "SKU数量不能为空";
        }
        if (importRequest.getSkuNum() <= 0) {
            return "SKU数量必须大于0";
        }

        // 验证是否主品和是否赠品的字符串格式
        if (StrUtil.isNotBlank(importRequest.getMainStr()) &&
                !"是".equals(importRequest.getMainStr().trim()) &&
                !"否".equals(importRequest.getMainStr().trim())) {
            return "是否主品只能填写'是'或'否'";
        }

        if (StrUtil.isNotBlank(importRequest.getGiftStr()) &&
                !"是".equals(importRequest.getGiftStr().trim()) &&
                !"否".equals(importRequest.getGiftStr().trim())) {
            return "是否赠品只能填写'是'或'否'";
        }

        return null;
    }

    /**
     * 验证组合品数据的业务规则
     *
     * @param combinationName 组合品名称
     * @param importRequests  同一组合品的导入请求列表
     * @return 验证错误信息，如果为空则验证通过
     */
    public static String validateCombinationBusinessRules(String combinationName,
                                                          List<CombinationBatchImportRequest> importRequests) {
        if (importRequests == null || importRequests.isEmpty()) {
            return "组合品[" + combinationName + "]没有SKU明细";
        }

        // 验证是否有主品
        long mainCount = importRequests.stream()
                .filter(request -> request.getMain() != null && request.getMain())
                .count();

        if (mainCount == 0) {
            return "组合品[" + combinationName + "]必须有一个主品";
        }

        if (mainCount > 1) {
            return "组合品[" + combinationName + "]只能有一个主品";
        }

        // 验证SKU编码不能重复
        long distinctSkuCount = importRequests.stream()
                .map(CombinationBatchImportRequest::getSkuCode)
                .distinct()
                .count();

        if (distinctSkuCount != importRequests.size()) {
            return "组合品[" + combinationName + "]中存在重复的SKU编码";
        }

        return null;
    }

    /**
     * 验证SKU编码和名称的一致性
     *
     * @param combinationName 组合品名称
     * @param importRequests  同一组合品的导入请求列表
     * @param skuList         系统中的SKU列表
     * @return 验证错误信息，如果为空则验证通过
     */
    public static String validateSkuCodeNameConsistency(String combinationName,
                                                        List<CombinationBatchImportRequest> importRequests,
                                                        List<com.spes.sop.goods.service.sku.model.bo.SkuListBO> skuList) {
        // 构建SKU编码到名称的映射
        Map<String, String> skuCodeToNameMap = skuList.stream()
                .collect(Collectors.toMap(
                        SkuListBO::getSkuCode,
                        SkuListBO::getSkuName,
                        (existing, replacement) -> existing // 如果有重复的编码，保留第一个
                ));

        // 验证每个导入请求中的SKU编码和名称是否一致
        for (CombinationBatchImportRequest request : importRequests) {
            String expectedName = skuCodeToNameMap.get(request.getSkuCode());
            if (expectedName != null && !expectedName.equals(request.getSkuName())) {
                return "组合品[" + combinationName + "]中SKU编码[" + request.getSkuCode() +
                        "]对应的名称应为[" + expectedName + "]，但填写的是[" + request.getSkuName() + "]";
            }
        }

        return null;
    }

    /**
     * 创建导入结果
     *
     * @param totalCount   总条数
     * @param successCount 成功条数
     * @param failDetails  失败详情列表
     * @return 导入结果
     */
    public static com.spes.sop.controller.goods.model.vo.CombinationBatchImportResultVO buildImportResult(
            int totalCount, int successCount,
            List<com.spes.sop.controller.goods.model.vo.CombinationBatchImportResultVO.CombinationImportFailDetail> failDetails) {
        return com.spes.sop.controller.goods.model.vo.CombinationBatchImportResultVO.builder()
                .totalCount(totalCount)
                .successCount(successCount)
                .failCount(failDetails.size())
                .failDetails(failDetails)
                .build();
    }

    /**
     * 创建空的导入结果
     *
     * @return 空的导入结果
     */
    public static com.spes.sop.controller.goods.model.vo.CombinationBatchImportResultVO buildEmptyResult() {
        return com.spes.sop.controller.goods.model.vo.CombinationBatchImportResultVO.builder()
                .totalCount(0)
                .successCount(0)
                .failCount(0)
                .failDetails(new ArrayList<>())
                .build();
    }

    /**
     * 创建失败详情
     *
     * @param rowNum          行号
     * @param combinationName 组合品名称
     * @param failReason      失败原因
     * @return 失败详情
     */
    public static com.spes.sop.controller.goods.model.vo.CombinationBatchImportResultVO.CombinationImportFailDetail createFailDetail(
            int rowNum, String combinationName, String failReason) {
        return com.spes.sop.controller.goods.model.vo.CombinationBatchImportResultVO.CombinationImportFailDetail.builder()
                .rowNum(rowNum)
                .combinationName(combinationName)
                .failReason(failReason)
                .build();
    }

    /**
     * 创建模板数据
     *
     * @return 模板数据列表
     */
    public static List<CombinationBatchImportRequest> createTemplateData() {
        List<CombinationBatchImportRequest> templateData = new ArrayList<>();

        // 添加示例数据
        templateData.add(CombinationBatchImportRequest.builder()
                .combinationName("示例组合品A")
                .description("示例组合品A的描述")
                .skuCode("SKU001")
                .skuName("示例SKU001名称")
                .skuNum(1)
                .mainStr("是")
                .giftStr("否")
                .build());

        templateData.add(CombinationBatchImportRequest.builder()
                .combinationName("示例组合品A")
                .description("示例组合品A的描述")
                .skuCode("SKU002")
                .skuName("示例SKU002名称")
                .skuNum(2)
                .mainStr("否")
                .giftStr("是")
                .build());

        return templateData;
    }
} 