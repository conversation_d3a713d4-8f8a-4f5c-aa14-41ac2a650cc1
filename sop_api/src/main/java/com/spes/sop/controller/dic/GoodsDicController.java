package com.spes.sop.controller.dic;

import com.google.common.collect.Lists;
import com.spes.sop.common.enums.OaStatusEnum;
import com.spes.sop.common.result.Result;
import com.spes.sop.controller.dic.model.convert.GoodsDicConvert;
import com.spes.sop.controller.dic.model.vo.ClassificationTreeVO;
import com.spes.sop.controller.dic.model.vo.CommonDicVO;
import com.spes.sop.controller.dic.model.vo.EnumVO;
import com.spes.sop.goods.service.common.GoodsCommonService;
import com.spes.sop.goods.service.common.model.bo.GoodsCommonBO;
import com.spes.sop.goods.service.common.model.bo.GoodsCommonTreeBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 商品相关字典接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/dic/goods")
public class GoodsDicController {

    private final GoodsCommonService goodsCommonService;

    /**
     * 获取渠道字典列表
     *
     * @return 渠道列表
     */
    @GetMapping("/channels")
    public Result<List<CommonDicVO>> getChannels() {
        List<GoodsCommonBO> channels = goodsCommonService.getChannels();
        return Result.success(GoodsDicConvert.convert(channels));
    }

    /**
     * 获取审批状态枚举字典
     *
     * @return 审批状态列表
     */
    @GetMapping("/statuses")
    public Result<List<EnumVO>> getStatuses() {
        List<EnumVO> result = Lists.newArrayList();
        for (OaStatusEnum value : OaStatusEnum.values()) {
            result.add(EnumVO.builder()
                    .name(value.name())
                    .desc(value.getDesc())
                    .build());
        }
        return Result.success(result);
    }

    /**
     * 获取系列字典列表
     *
     * @return 系列列表
     */
    @GetMapping("/series")
    public Result<List<CommonDicVO>> getSeries() {
        List<GoodsCommonBO> series = goodsCommonService.getSeries();
        return Result.success(GoodsDicConvert.convert(series));
    }

    /**
     * 获取产品分类字典列表
     *
     * @return 产品分类列表
     */
    @GetMapping("/categories")
    public Result<List<CommonDicVO>> getCategories() {
        List<GoodsCommonBO> category = goodsCommonService.getCategory();
        return Result.success(GoodsDicConvert.convert(category));
    }

    /**
     * 获取商品分类树形结构
     * @return 分类树形结构
     */
    @GetMapping("/classification")
    public Result<List<ClassificationTreeVO>> getClassificationTree() {
        List<GoodsCommonTreeBO> classificationTree = goodsCommonService.getClassificationTree();
        List<ClassificationTreeVO> result = GoodsDicConvert.convertTree(classificationTree);
        return Result.success(result);
    }

}
