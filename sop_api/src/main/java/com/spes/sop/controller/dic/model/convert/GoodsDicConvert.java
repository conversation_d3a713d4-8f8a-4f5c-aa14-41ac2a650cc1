package com.spes.sop.controller.dic.model.convert;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.spes.sop.controller.dic.model.vo.ClassificationTreeVO;
import com.spes.sop.controller.dic.model.vo.CommonDicVO;
import com.spes.sop.goods.service.common.model.bo.GoodsCommonBO;
import com.spes.sop.goods.service.common.model.bo.GoodsCommonTreeBO;

import java.util.List;
import java.util.stream.Collectors;

public interface GoodsDicConvert {
    static List<CommonDicVO> convert(List<GoodsCommonBO> category) {
        if (CollUtil.isEmpty(category)) {
            return Lists.newArrayList();
        }
        return category.stream()
                .map(c -> CommonDicVO.builder()
                        .id(c.getId())
                        .name(c.getName())
                        .build())
                .collect(Collectors.toList());
    }

    static List<ClassificationTreeVO> convertTree(List<GoodsCommonTreeBO> classificationTree) {
        if (CollUtil.isEmpty(classificationTree)) {
            return Lists.newArrayList();
        }
        return classificationTree.stream()
                .map(c -> ClassificationTreeVO.builder()
                        .id(c.getId())
                        .name(c.getName())
                        .children(convertTree(c.getChildren()))
                        .build())
                .collect(Collectors.toList());
    }
}
