package com.spes.sop.controller.goods.converter;

import com.spes.sop.common.page.PageResult;
import com.spes.sop.controller.goods.model.query.SpuPageQuery;
import com.spes.sop.controller.goods.model.request.SpuCreateRequest;
import com.spes.sop.controller.goods.model.request.SpuUpdateRequest;
import com.spes.sop.controller.goods.model.vo.SpuPageVO;
import com.spes.sop.goods.service.spu.model.bo.SpuBO;
import com.spes.sop.goods.service.spu.model.command.SpuCreateCommand;
import com.spes.sop.goods.service.spu.model.query.SpuBOListQuery;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SPU DTO转换器
 *
 * <AUTHOR>

 */
public class SpuConverter {

    /**
     * 转换创建请求为业务对象
     */
    public static SpuCreateCommand convert2Command(SpuCreateRequest request) {
        if (request == null) {
            return null;
        }

        return SpuCreateCommand.builder()
                .spuName(request.getSpuName())
                .description(request.getDescription())
                .firstClassification(request.getFirstClassification())
                .secondClassification(request.getSecondClassification())
                .thirdClassification(request.getThirdClassification())
                .build();
    }

    /**
     * 转换更新请求为业务对象
     */
    public static SpuBO convertUpdateRequestToBO(SpuUpdateRequest request) {
        if (request == null) {
            return null;
        }

        return SpuBO.builder()
                .id(request.getId())
                .spuName(request.getSpuName())
                .description(request.getDescription())
                .firstClassification(request.getFirstClassification())
                .secondClassification(request.getSecondClassification())
                .thirdClassification(request.getThirdClassification())
                .build();
    }

    /**
     * 转换分页请求为业务查询对象
     */
    public static SpuBOListQuery convertPageRequestToQuery(SpuPageQuery request) {
        if (request == null) {
            return null;
        }
        return null;

//        return SpuBOListQuery.builder()
//                .spuCode(request.getSpuCode())
//                .firstClassification(request.getFirstClassification())
//                .secondClassification(request.getSecondClassification())
//                .thirdClassification(request.getThirdClassification())
//                .pageNum(request.getPageNum())
//                .pageSize(request.getPageSize())
//                .orderBy("create_time")
//                .orderDirection("DESC")
//                .build();
    }

    /**
     * 转换业务对象为响应DTO
     */
    public static SpuPageVO convertBOToResponse(SpuBO spuBO) {
        if (spuBO == null) {
            return null;
        }

        SpuPageVO response = new SpuPageVO();
        BeanUtils.copyProperties(spuBO, response);
        return response;
    }

    /**
     * 转换业务对象列表为响应DTO列表
     */
    public static List<SpuPageVO> convertBOListToResponseList(List<SpuBO> spuBOList) {
        if (CollectionUtils.isEmpty(spuBOList)) {
            return Collections.emptyList();
        }

        return spuBOList.stream()
                .map(SpuConverter::convertBOToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 转换分页业务结果为分页响应VO
     */
    public static PageResult<SpuPageVO> convertPageResultToResponse(PageResult<SpuBO> pageResult) {
        if (pageResult == null) {
            return null;
        }

        List<SpuPageVO> responseList = convertBOListToResponseList(pageResult.getRecords());


        return PageResult.of(responseList, pageResult.getTotal(), pageResult.getPageNum(), pageResult.getPageSize());
    }
} 
