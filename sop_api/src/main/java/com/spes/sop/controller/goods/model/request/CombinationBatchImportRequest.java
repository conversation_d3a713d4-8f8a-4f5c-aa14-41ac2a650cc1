package com.spes.sop.controller.goods.model.request;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 组合品批量导入请求模型
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationBatchImportRequest {

    /**
     * 组合品名称
     */
    @ExcelProperty(value = "组合品名称", index = 0)
    private String combinationName;

    /**
     * 组合品描述
     */
    @ExcelProperty(value = "组合品描述", index = 1)
    private String description;

    /**
     * SKU编码
     */
    @ExcelProperty(value = "SKU编码", index = 2)
    private String skuCode;

    /**
     * SKU名称
     */
    @ExcelProperty(value = "SKU名称", index = 3)
    private String skuName;

    /**
     * SKU数量
     */
    @ExcelProperty(value = "SKU数量", index = 4)
    private Integer skuNum;

    /**
     * 是否主品（是/否）
     */
    @ExcelProperty(value = "是否主品", index = 5)
    private String mainStr;

    /**
     * 是否赠品（是/否）
     */
    @ExcelProperty(value = "是否赠品", index = 6)
    private String giftStr;

    // 转换方法：将字符串转换为布尔值
    public Boolean getMain() {
        if (mainStr == null) {
            return false;
        }
        return "是".equals(mainStr.trim());
    }

    public Boolean getGift() {
        if (giftStr == null) {
            return false;
        }
        return "是".equals(giftStr.trim());
    }
} 