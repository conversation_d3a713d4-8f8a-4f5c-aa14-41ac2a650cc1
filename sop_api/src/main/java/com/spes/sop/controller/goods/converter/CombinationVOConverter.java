package com.spes.sop.controller.goods.converter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.spes.sop.common.enums.OaStatusEnum;
import com.spes.sop.common.page.BasePager;
import com.spes.sop.common.security.JwtUserDetails;
import com.spes.sop.controller.goods.model.query.CombinationPageQuery;
import com.spes.sop.controller.goods.model.request.CombinationCreateRequest;
import com.spes.sop.controller.goods.model.request.CombinationUpdateRequest;
import com.spes.sop.controller.goods.model.vo.CombinationDetailVO;
import com.spes.sop.controller.goods.model.vo.CombinationPageVO;
import com.spes.sop.controller.goods.model.vo.CombinationSkuItemVO;
import com.spes.sop.goods.service.combination.model.bo.CombinationDetailBO;
import com.spes.sop.goods.service.combination.model.bo.CombinationListBO;
import com.spes.sop.goods.service.combination.model.bo.CombinationSkuRelationBO;
import com.spes.sop.goods.service.combination.model.command.CombinationCreateCommand;
import com.spes.sop.goods.service.combination.model.command.CombinationSkuRelationCreateCommand;
import com.spes.sop.goods.service.combination.model.command.CombinationSkuRelationUpdateCommand;
import com.spes.sop.goods.service.combination.model.command.CombinationUpdateCommand;
import com.spes.sop.goods.service.combination.model.query.CombinationBOPageQuery;
import com.spes.sop.goods.service.common.model.bo.GoodsCommonBO;
import com.spes.sop.goods.service.sku.model.bo.SkuDetailBO;
import com.spes.sop.goods.service.sku.model.bo.SkuListBO;
import com.spes.sop.goods.service.spu.model.bo.SpuBO;
import com.spes.sop.third.weaver.constants.WeaverWorkflowConstant;
import com.spes.sop.third.weaver.model.request.value.WorkflowRequestTable;
import com.spes.sop.third.weaver.model.request.value.WorkflowRequestTableField;
import com.spes.sop.third.weaver.model.request.value.WorkflowRequestTableRecord;
import com.spes.sop.user.service.user.model.bo.UserBO;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 组合品Controller层转换器
 * 负责Controller层模型与Service层模型之间的转换
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
public class CombinationVOConverter {

    /**
     * 将CombinationCreateRequest转换为CombinationCreateCommand
     *
     * @param request    创建请求
     * @param operatorId 操作人ID
     * @return 服务层创建命令
     */
    public static CombinationCreateCommand toCreateCommand(@NotNull CombinationCreateRequest request,
                                                           @NotNull Long operatorId, @NotNull SkuDetailBO sku,
                                                           SpuBO spuInfo) {
        if (request == null) {
            return null;
        }
        if (sku == null) {
            throw new IllegalArgumentException("主品SKU信息不能为空");
        }

        // 转换SKU关联项
        List<CombinationSkuRelationCreateCommand> skuItems = null;
        if (CollUtil.isNotEmpty(request.getSkuIds())) {
            skuItems = request.getSkuIds().stream()
                    .map(item -> CombinationSkuRelationCreateCommand.builder()
                            .skuId(item.getSkuId())
                            .amount(item.getSkuNum())
                            .main(item.getMain())
                            .gift(item.getGift())
                            .build())
                    .collect(Collectors.toList());
        }

        return CombinationCreateCommand.builder()
                .name(request.getCombinationName())
                .description(request.getDescription())
                .seriesId(sku.getSeriesIds())
                .categoryId(sku.getCategoryId())
                .firstClassification(spuInfo.getFirstClassification())
                .secondClassification(spuInfo.getSecondClassification())
                .thirdClassification(spuInfo.getThirdClassification())
                .channelId(sku.getChannelId())
                .skuItems(skuItems)
                .operatorId(operatorId)
                .build();
    }

    public static CombinationBOPageQuery toServiceQuery(@NotNull CombinationPageQuery request) {
        return CombinationBOPageQuery.builder()
                .combinationNameSearch(request.getCombinationNameSearch())
                .combinationCode(request.getCombinationCode())
                .firstClassification(ObjectUtil.isNull(request.getFirstClassification()) ? null :
                        Collections.singletonList(request.getFirstClassification()))
                .secondClassification(ObjectUtil.isNull(request.getSecondClassification()) ? null :
                        Collections.singletonList(request.getSecondClassification()))
                .thirdClassification(ObjectUtil.isNull(request.getThirdClassification()) ? null :
                        Collections.singletonList(request.getThirdClassification()))
                .statuses(CollUtil.isEmpty(request.getStatuses()) ? null :
                        request.getStatuses().stream().map(OaStatusEnum::getByName).collect(Collectors.toList()))
                .seriesId(request.getSeriesId())
                .categoryIds(request.getCategoryIds())
                .gift(request.getGift())
                .packageCard(request.getPackageCard())
                .pager(new BasePager.Pager(request.getPageNum(), request.getPageSize()))
                .build();
    }

    public static List<CombinationPageVO> toListVO(List<CombinationListBO> combinations,
                                                   List<SkuListBO> skus,
                                                   List<GoodsCommonBO> categories,
                                                   List<GoodsCommonBO> channels,
                                                   List<GoodsCommonBO> series,
                                                   List<GoodsCommonBO> classifications,
                                                   List<UserBO> users) {
        Map<Long, String> skuMap = CollUtil.isNotEmpty(skus) ?
                skus.stream().collect(Collectors.toMap(SkuListBO::getId, SkuListBO::getSkuName)) : Maps.newHashMap();
        Map<Long, String> seriesMap = CollUtil.isNotEmpty(series) ?
                series.stream().collect(Collectors.toMap(GoodsCommonBO::getId, GoodsCommonBO::getName)) :
                Maps.newHashMap();
        Map<Long, String> classificationMap = CollUtil.isNotEmpty(classifications) ?
                classifications.stream().collect(Collectors.toMap(GoodsCommonBO::getId, GoodsCommonBO::getName)) :
                Maps.newHashMap();
        Map<Long, String> categoryMap = CollUtil.isNotEmpty(categories) ?
                categories.stream().collect(Collectors.toMap(GoodsCommonBO::getId, GoodsCommonBO::getName)) :
                Maps.newHashMap();
        Map<Long, String> channelMap = CollUtil.isNotEmpty(channels) ?
                channels.stream().collect(Collectors.toMap(GoodsCommonBO::getId, GoodsCommonBO::getName)) :
                Maps.newHashMap();
        Map<Long, String> userMap = CollUtil.isNotEmpty(users) ?
                users.stream().collect(Collectors.toMap(UserBO::getId, UserBO::getUsername)) :
                Maps.newHashMap();
        return toListVO(combinations, skuMap, seriesMap, classificationMap, categoryMap,
                channelMap, userMap);

    }

    public static List<CombinationPageVO> toListVO(List<CombinationListBO> combinations,
                                                   Map<Long, String> skuMap,
                                                   Map<Long, String> seriesMap,
                                                   Map<Long, String> classificationMap,
                                                   Map<Long, String> categoryMap,
                                                   Map<Long, String> channelMap,
                                                   Map<Long, String> userMap) {
        return combinations.stream()
                .map(combination -> toPageVO(combination, skuMap, seriesMap, classificationMap, categoryMap,
                        channelMap, userMap))
                .collect(Collectors.toList());
    }

    private static CombinationPageVO toPageVO(CombinationListBO combinationListBO,
                                              Map<Long, String> skuMap,
                                              Map<Long, String> seriesMap,
                                              Map<Long, String> classificationMap,
                                              Map<Long, String> categoryMap,
                                              Map<Long, String> channelMap,
                                              Map<Long, String> userMap) {
        return CombinationPageVO.builder()
                .id(combinationListBO.getId())
                .combinationName(combinationListBO.getName())
                .combinationCode(combinationListBO.getCombinationCode())
                .description(combinationListBO.getDescription())
                .status(combinationListBO.getOaStatus().name())
                .statusDesc(combinationListBO.getOaStatus().getDesc())
                .skuNames((CollUtil.isNotEmpty(combinationListBO.getSkuIds()) && CollUtil.isNotEmpty(skuMap)) ?
                        combinationListBO.getSkuIds().stream().map(skuMap::get).collect(Collectors.toList()) :
                        Lists.newArrayList())
                .series((CollUtil.isNotEmpty(combinationListBO.getSeriesIds()) && CollUtil.isNotEmpty(seriesMap)) ?
                        combinationListBO.getSeriesIds().stream().map(seriesMap::get).collect(Collectors.toList()) :
                        Lists.newArrayList())
                .category((ObjectUtil.isNotNull(combinationListBO.getCategoryId()) && CollUtil.isNotEmpty(categoryMap)) ?
                        categoryMap.get(combinationListBO.getCategoryId()) : null)
                .channel((ObjectUtil.isNotNull(combinationListBO.getChannelId()) && CollUtil.isNotEmpty(channelMap)) ?
                        channelMap.get(combinationListBO.getChannelId()) : null)
                .firstClassification((ObjectUtil.isNotNull(combinationListBO.getFirstClassification()) && CollUtil.isNotEmpty(classificationMap)) ?
                        classificationMap.get(combinationListBO.getFirstClassification()) : null)
                .secondClassification((ObjectUtil.isNotNull(combinationListBO.getSecondClassification()) && CollUtil.isNotEmpty(classificationMap)) ?
                        classificationMap.get(combinationListBO.getSecondClassification()) : null)
                .thirdClassification((ObjectUtil.isNotNull(combinationListBO.getThirdClassification()) && CollUtil.isNotEmpty(classificationMap)) ?
                        classificationMap.get(combinationListBO.getThirdClassification()) : null)
                .createTime(combinationListBO.getCreateTime())
                .updateTime(combinationListBO.getUpdateTime())
                .creatorName(CollUtil.isNotEmpty(userMap) ? userMap.get(combinationListBO.getCreatorId()) : null)
                .updaterName(CollUtil.isNotEmpty(userMap) ? userMap.get(combinationListBO.getUpdaterId()) : null)
                .updaterName(combinationListBO.getUpdaterName())
                .creatorId(combinationListBO.getCreatorId())
                .updaterId(combinationListBO.getUpdaterId())
                .build();
    }

    public static CombinationDetailVO toDetailVO(CombinationDetailBO combination,
                                                 List<SkuListBO> skus,
                                                 List<GoodsCommonBO> categories,
                                                 List<GoodsCommonBO> channels,
                                                 List<GoodsCommonBO> series,
                                                 List<GoodsCommonBO> classifications) {

        // 构建SKU映射，便于后续查找SKU信息
        Map<Long, SkuListBO> skuMap = CollUtil.isNotEmpty(skus) ?
                skus.stream().collect(Collectors.toMap(SkuListBO::getId, sku -> sku)) : Maps.newHashMap();

        // 按版本分组构建SKU列表
        Map<Integer, List<CombinationSkuItemVO>> skuItems = buildSkuItemsByVersion(combination.getSkuRelations(),
                skuMap);

        return CombinationDetailVO.builder()
                .id(combination.getId())
                .combinationCode(combination.getCombinationCode())
                .combinationName(combination.getName())
                .description(combination.getDescription())
                .status(combination.getOaStatus().name())
                .statusDesc(combination.getOaStatus().getDesc())
                .firstClassification(findClassificationName(combination.getFirstClassification(), classifications))
                .secondClassification(findClassificationName(combination.getSecondClassification(), classifications))
                .thirdClassification(findClassificationName(combination.getThirdClassification(), classifications))
                .series(extractNames(series))
                .category(extractFirstName(categories))
                .channel(extractFirstName(channels))
                .skuItems(skuItems)
                .createTime(combination.getCreateTime())
                .updateTime(combination.getUpdateTime())
                .creatorId(combination.getCreatorId())
                .updaterId(combination.getUpdaterId())
                .build();
    }

    /**
     * 查找分类名称
     */
    private static String findClassificationName(Long classificationId, List<GoodsCommonBO> classifications) {
        if (classificationId == null || CollUtil.isEmpty(classifications)) {
            return null;
        }
        return classifications.stream()
                .filter(c -> c.getId().equals(classificationId))
                .map(GoodsCommonBO::getName)
                .findFirst()
                .orElse(null);
    }

    /**
     * 提取名称列表
     */
    private static List<String> extractNames(List<GoodsCommonBO> list) {
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.stream()
                .map(GoodsCommonBO::getName)
                .collect(Collectors.toList());
    }

    /**
     * 提取第一个名称
     */
    private static String extractFirstName(List<GoodsCommonBO> list) {
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.get(0).getName();
    }

    /**
     * 按版本号分组SKU构建skuItems
     *
     * @param skuRelations SKU关联关系BO列表
     * @param skuMap       SKU映射（ID -> SkuListBO）
     * @return 按版本号分组的SKU列表
     */
    private static Map<Integer, List<CombinationSkuItemVO>> buildSkuItemsByVersion(
            List<CombinationSkuRelationBO> skuRelations, Map<Long, SkuListBO> skuMap) {

        if (CollUtil.isEmpty(skuRelations)) {
            return Maps.newHashMap();
        }

        return skuRelations.stream()
                .collect(Collectors.groupingBy(CombinationSkuRelationBO::getCombinationVersion))
                .entrySet()
                .stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                                .map(relation -> {
                                    SkuListBO sku = skuMap.get(relation.getSkuId());
                                    return sku != null ? convertToCombinationSkuItemVO(sku, relation) : null;
                                })
                                .filter(ObjectUtil::isNotNull)
                                .collect(Collectors.toList())
                ));
    }

    /**
     * 将CombinationUpdateRequest转换为CombinationUpdateCommand
     *
     * @param request    更新请求
     * @param operatorId 操作人ID
     * @return 服务层更新命令
     */
    public static CombinationUpdateCommand toUpdateCommand(@NotNull CombinationUpdateRequest request,
                                                           @NotNull Long operatorId) {
        if (request == null) {
            return null;
        }

        // 转换SKU关联项
        List<CombinationSkuRelationUpdateCommand> skuItems = null;
        if (CollUtil.isNotEmpty(request.getSkuItems())) {
            Date effectTime = new Date();
            skuItems = request.getSkuItems().stream()
                    .map(item -> CombinationSkuRelationUpdateCommand.builder()
                            .skuId(item.getSkuId())
                            .amount(item.getSkuNum())
                            .main(item.getMain())
                            .gift(item.getGift())
                            .effectiveTime(effectTime)
                            .build())
                    .collect(Collectors.toList());
        }

        return CombinationUpdateCommand.builder()
                .id(request.getId())
                .name(request.getCombinationName())
                .description(request.getDescription())
                .skuItems(skuItems)
                .operatorId(operatorId)
                .build();
    }

    public static Map<String, Object> convert2workflowRequest(CombinationCreateCommand command, JwtUserDetails user,
                                                              List<SkuListBO> skuInfos) {
        Map<String, Object> params = new HashMap<>();
        params.put("requestName", generateComboRequestName(command));
        params.put("mainData", getMainData(user));
        params.put("workflowId", WeaverWorkflowConstant.WorkflowId.COMBINATION_APPROVAL_WORKFLOW_ID);
        params.put("detailData", getComboDetailData(command, skuInfos));
        return params;
    }

    private static String getComboDetailData(CombinationCreateCommand command, List<SkuListBO> skuInfos) {
        if (CollUtil.isEmpty(command.getSkuItems())) {
            return JSONObject.toJSONString(new ArrayList<>());
        }
        Map<Long, SkuListBO> skus = skuInfos.stream().collect(Collectors.toMap(SkuListBO::getId, Function.identity(),
                (a, b) -> b));
        List<WorkflowRequestTable> detailData = new ArrayList<>();
        WorkflowRequestTable relationTable = new WorkflowRequestTable();
        relationTable.setTableDBName("formtable_main_248_dt1");
        relationTable.setWorkflowRequestTableRecords(new ArrayList<>(command.getSkuItems().size()));
        for (int i = 0; i < command.getSkuItems().size(); i++) {
            CombinationSkuRelationCreateCommand relation = command.getSkuItems().get(i);
            SkuListBO skuInfo = skus.get(relation.getSkuId());
            if (ObjectUtil.isNull(skuInfo)) {
                continue;
            }
            WorkflowRequestTableRecord businessTableRecord = new WorkflowRequestTableRecord();
            businessTableRecord.setRecordOrder(0);
            businessTableRecord.setWorkflowRequestTableFields(buildTableFields(command, relation, skuInfo));
            relationTable.getWorkflowRequestTableRecords().add(businessTableRecord);
        }
        detailData.add(relationTable);
        return JSONObject.toJSONString(detailData);
    }

    private static List<WorkflowRequestTableField> buildTableFields(CombinationCreateCommand createCommand,
                                                                    CombinationSkuRelationCreateCommand command,
                                                                    SkuListBO skuInfo) {
        List<WorkflowRequestTableField> tableFields = new ArrayList<>();
        tableFields.add(new WorkflowRequestTableField("skuCode", skuInfo.getSkuCode()));
        tableFields.add(new WorkflowRequestTableField("skuName", skuInfo.getSkuName()));
        tableFields.add(new WorkflowRequestTableField("barCode", skuInfo.getBarCode()));
        tableFields.add(new WorkflowRequestTableField("amount", String.valueOf(command.getAmount())));
        tableFields.add(new WorkflowRequestTableField("combinationCode", createCommand.getCombinationCode()));
        tableFields.add(new WorkflowRequestTableField("combinationName", createCommand.getName()));
        tableFields.add(new WorkflowRequestTableField("description", createCommand.getDescription()));
        tableFields.add(new WorkflowRequestTableField("main", command.getMain() ? "1" : "0"));
        tableFields.add(new WorkflowRequestTableField("gift", command.getGift() ? "1" : "0"));
        return tableFields;
    }


    private static String getMainData(JwtUserDetails user) {
        List<WorkflowRequestTableField> mainData = new ArrayList<>();
        //附件上传字段
        WorkflowRequestTableField field1 = new WorkflowRequestTableField();
        field1.setFieldName("createTime");
        field1.setFieldValue(DateUtil.format(new Date(), "yyyy-MM-dd"));
        mainData.add(field1);

        //单行文本字段
        WorkflowRequestTableField field2 = new WorkflowRequestTableField();
        field2.setFieldName("departmentName");
        field2.setFieldValue(user.getDepartment());
        mainData.add(field2);

        WorkflowRequestTableField field3 = new WorkflowRequestTableField();
        field3.setFieldName("creatorName");
        field3.setFieldValue(String.valueOf(user.getWeaverId()));
        mainData.add(field3);

        return JSONObject.toJSONString(mainData);
    }

    private static String generateComboRequestName(CombinationCreateCommand command) {
        String today = DateUtil.format(new Date(), "yyyy-MM-dd");
        return String.format("组合品审批-%s-%s", command.getName(), today);
    }

    /**
     * 将SkuListBO和CombinationSkuRelationBO转换为CombinationSkuItemVO
     *
     * @param sku      SKU信息
     * @param relation 组合品SKU关联关系
     * @return 组合品SKU项VO
     */
    private static CombinationSkuItemVO convertToCombinationSkuItemVO(SkuListBO sku,
                                                                      CombinationSkuRelationBO relation) {
        return CombinationSkuItemVO.builder()
                .id(sku.getId())
                .skuCode(sku.getSkuCode())
                .skuName(sku.getSkuName())
                .barCode(sku.getBarCode())
                .spec(sku.getSpecification())
                .skuNum(relation.getAmount())
                .main(relation.getMain())
                .gift(relation.getGift())
                .fairPrice(sku.getFairPrice())
                .basePrice(sku.getBasePrice())
                .cost(sku.getCost())
                .build();
    }
}