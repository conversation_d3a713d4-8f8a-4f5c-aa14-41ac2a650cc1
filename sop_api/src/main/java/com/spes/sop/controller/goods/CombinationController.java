package com.spes.sop.controller.goods;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.spes.sop.common.annotation.OperationLog;
import com.spes.sop.common.page.PageResult;
import com.spes.sop.common.result.Result;
import com.spes.sop.controller.goods.model.convert.CombinationVOConverter;
import com.spes.sop.controller.goods.model.query.CombinationPageQuery;
import com.spes.sop.controller.goods.model.request.CombinationCreateRequest;
import com.spes.sop.controller.goods.model.request.CombinationUpdateRequest;
import com.spes.sop.controller.goods.model.vo.CombinationBatchImportResultVO;
import com.spes.sop.controller.goods.model.vo.CombinationDetailVO;
import com.spes.sop.controller.goods.model.vo.CombinationPageVO;
import com.spes.sop.support.goods.CombinationSupport;
import com.spes.sop.support.goods.model.dto.CombinationDetailDTO;
import com.spes.sop.support.goods.model.dto.CombinationPageDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;

/**
 * 组合品管理控制器
 * 提供组合品的增删改查等功能
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/combination")
public class CombinationController {

    private final CombinationSupport combinationSupport;

    /**
     * 组合品分页查询
     *
     * @param request 分页查询请求
     * @return 分页结果
     */
    @PostMapping("/page")
    public Result<PageResult<CombinationPageVO>> page(@Validated @RequestBody @NotNull CombinationPageQuery request) {

        log.info("分页查询组合品列表，查询条件：{}", request);
        PageResult<CombinationPageDTO> page = combinationSupport.page(request);
        if (ObjectUtil.isNull(page) || CollUtil.isEmpty(page.getRecords())) {
            return Result.error("查询组合品列表失败");
        }
        PageResult<CombinationPageVO> result = PageResult.of(CombinationVOConverter.toListVO(page.getRecords()),
                page.getTotal(), request.getPageNum(), request.getPageSize());
        return Result.success(result);
    }

    /**
     * 根据ID查询组合品详情
     *
     * @param id 组合品ID
     * @return 组合品详情
     */
    @GetMapping("/get/{id}")
    public Result<CombinationDetailVO> getById(@PathVariable @NotNull Long id) {
        CombinationDetailDTO combination = combinationSupport.getById(id);
        if (combination == null) {
            return Result.error("组合品不存在");
        }
        CombinationDetailVO combinationVO = CombinationVOConverter.toDetailVO(combination);
        return Result.success(combinationVO);
    }

    /**
     * 新增组合品
     *
     * @param request 新增请求
     * @return 创建成功的组合品ID
     */
    @PostMapping
    public Result<Void> create(@Validated @RequestBody @NotNull CombinationCreateRequest request) {

        log.info("新增组合品，请求参数：{}", request);
        combinationSupport.create(request);
        return Result.success();
    }
    /**
     * 更新组合品信息
     *
     * @param request 更新请求
     * @return 操作结果
     */
    @PostMapping("/update")
    public Result<Void> update(@Validated @RequestBody CombinationUpdateRequest request) {
        combinationSupport.update(request);
        return Result.success();
    }


    /**
     * 检查组合品名称是否存在
     *
     * @param name      组合品名称
     * @param excludeId 排除的ID（可选）
     * @return 是否存在
     */
    @GetMapping("/check/name")
    public Result<Boolean> checkNameExists(@RequestParam String name,
                                           @RequestParam(required = false) Long excludeId) {
        return Result.success(combinationSupport.existsByName(name, excludeId));
    }

    /**
     * 批量导入组合品
     *
     * @param file Excel文件
     * @return 导入结果
     */
    @OperationLog("批量导入组合品")
    @PostMapping("/import")
    public Result<CombinationBatchImportResultVO> batchImport(@RequestParam("file") MultipartFile file) {
        log.info("开始批量导入组合品，文件名：{}", file.getOriginalFilename());

        // 验证文件
        if (file.isEmpty()) {
            return Result.error("上传文件不能为空");
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
            return Result.error("只支持Excel文件格式(.xlsx或.xls)");
        }

        try {
            CombinationBatchImportResultVO result = combinationSupport.batchImport(file);
            return Result.success(result);
        } catch (Exception e) {
            log.error("批量导入组合品失败", e);
            return Result.error("批量导入失败：" + e.getMessage());
        }
    }

    /**
     * 下载组合品导入模板
     *
     * @param response HTTP响应
     */
    @GetMapping("/template")
    public void downloadTemplate(HttpServletResponse response) {
        log.info("下载组合品导入模板");
        try {
            combinationSupport.downloadTemplate(response);
        } catch (Exception e) {
            log.error("下载组合品导入模板失败", e);
            throw new RuntimeException("下载模板失败：" + e.getMessage());
        }
    }
}