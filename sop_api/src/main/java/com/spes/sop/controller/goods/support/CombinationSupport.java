package com.spes.sop.controller.goods.support;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.aliyuncs.utils.StringUtils;
import com.google.common.collect.Lists;
import com.spes.sop.common.enums.OaStatusEnum;
import com.spes.sop.common.exception.BusinessException;
import com.spes.sop.common.page.PageResult;
import com.spes.sop.common.security.JwtUserDetails;
import com.spes.sop.common.security.SecurityContextUtil;
import com.spes.sop.common.util.CodeGenerationUtil;
import com.spes.sop.controller.goods.converter.CombinationVOConverter;
import com.spes.sop.controller.goods.model.query.CombinationPageQuery;
import com.spes.sop.controller.goods.model.request.CombinationCreateRequest;
import com.spes.sop.controller.goods.model.request.CombinationUpdateRequest;
import com.spes.sop.controller.goods.model.request.value.CombinationSkuItem;
import com.spes.sop.controller.goods.model.vo.CombinationDetailVO;
import com.spes.sop.controller.goods.model.vo.CombinationPageVO;
import com.spes.sop.controller.goods.model.vo.value.CombinationRelatedIds;
import com.spes.sop.goods.service.combination.CombinationService;
import com.spes.sop.goods.service.combination.model.bo.CombinationDetailBO;
import com.spes.sop.goods.service.combination.model.bo.CombinationListBO;
import com.spes.sop.goods.service.combination.model.bo.CombinationSkuRelationBO;
import com.spes.sop.goods.service.combination.model.command.CombinationCreateCommand;
import com.spes.sop.goods.service.combination.model.command.CombinationUpdateCommand;
import com.spes.sop.goods.service.combination.model.query.CombinationBOPageQuery;
import com.spes.sop.goods.service.common.GoodsCommonService;
import com.spes.sop.goods.service.common.model.bo.GoodsCommonBO;
import com.spes.sop.goods.service.sku.SkuService;
import com.spes.sop.goods.service.sku.model.bo.SkuDetailBO;
import com.spes.sop.goods.service.sku.model.bo.SkuListBO;
import com.spes.sop.goods.service.sku.model.query.SkuBOListQuery;
import com.spes.sop.goods.service.spu.SpuService;
import com.spes.sop.goods.service.spu.model.bo.SpuBO;
import com.spes.sop.third.weaver.service.WeaverWorkflowService;
import com.spes.sop.user.service.user.UserService;
import com.spes.sop.user.service.user.model.bo.UserBO;
import com.spes.sop.user.service.user.model.query.UserPageQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 组合商品支持类，提供组合商品相关的业务逻辑支持
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CombinationSupport {

    private final CombinationService combinationService;
    private final GoodsCommonService goodsCommonService;
    private final SkuService skuService;
    private final SpuService spuService;
    private final TransactionTemplate transactionTemplate;
    private final WeaverWorkflowService weaverWorkflowService;
    private final UserService userService;

    /**
     * 分页查询组合商品列表
     *
     * @param request 分页查询请求参数，不能为空
     * @return 分页结果，包含组合商品列表和分页信息
     * @throws IllegalArgumentException 当请求参数为空时抛出
     */
    public PageResult<CombinationPageVO> page(@NotNull CombinationPageQuery request) {
        Objects.requireNonNull(request, "分页查询请求参数不能为空");

        log.debug("开始分页查询组合商品，页码：{}，页大小：{}", request.getPageNum(), request.getPageSize());

        CombinationBOPageQuery query = CombinationVOConverter.toServiceQuery(request);
        Long count = combinationService.count(query);

        // 优化：使用更清晰的条件判断
        if (count == null || count <= 0) {
            log.debug("查询结果为空，返回空分页结果");
            return PageResult.of(Collections.emptyList(), 0L, request.getPageNum(), request.getPageSize());
        }

        List<CombinationListBO> combinations = combinationService.list(query);
        if (CollectionUtils.isEmpty(combinations)) {
            log.debug("组合商品列表为空，返回空分页结果");
            return PageResult.of(Collections.emptyList(), count, request.getPageNum(), request.getPageSize());
        }

        // 优化：批量收集所有需要的ID，减少Stream操作次数
        CombinationRelatedIds relatedIds = collectRelatedIds(combinations);

        // 并行获取关联数据以提升性能
        List<SkuListBO> skus = getSkusSafely(relatedIds.getSkuIds());
        List<GoodsCommonBO> categories = getCategoriesSafely(relatedIds.getCategoryIds());
        List<GoodsCommonBO> channels = getChannelsSafely(relatedIds.getChannelIds());
        List<GoodsCommonBO> series = getSeriesSafely(relatedIds.getSeriesIds());
        List<GoodsCommonBO> classifications = getClassificationsSafely(relatedIds.getClassificationIds());

        // 创建人信息
        List<Long> userIds = combinations.stream().map(CombinationListBO::getCreatorId).collect(Collectors.toList());
        List<UserBO> users = userService.getUserPage(UserPageQuery.builder().ids(userIds).build());


        List<CombinationPageVO> data = CombinationVOConverter.toListVO(combinations, skus, categories, channels,
                series, classifications, users);

        log.debug("分页查询组合商品完成，返回{}条记录", data.size());
        return PageResult.of(data, count, request.getPageNum(), request.getPageSize());
    }

    /**
     * 根据ID获取组合商品详情
     *
     * @param id 组合商品ID，不能为空
     * @return 组合商品详情，如果不存在则返回null
     * @throws IllegalArgumentException 当ID为空时抛出
     */
    public CombinationDetailVO getById(@NotNull Long id) {
        Objects.requireNonNull(id, "组合商品ID不能为空");

        log.debug("开始获取组合商品详情，ID：{}", id);

        CombinationDetailBO combination = combinationService.getById(id);
        if (combination == null) {
            log.debug("组合商品不存在，ID：{}", id);
            return null;
        }

        // 获取所有关联的SKU信息（包括生效和失效的）
        List<Long> allSkuIds = CollUtil.isNotEmpty(combination.getSkuRelations()) ?
                combination.getSkuRelations().stream()
                        .map(CombinationSkuRelationBO::getSkuId)
                        .distinct()
                        .collect(Collectors.toList()) : Collections.emptyList();
        List<SkuListBO> skus = getSkusSafely(allSkuIds);
        
        // 安全获取关联数据
        List<GoodsCommonBO> series = getSeriesSafely(combination.getSeriesId());

        // 优化：安全构建分类ID列表
        List<Long> classificationIds = buildClassificationIds(
                combination.getFirstClassification(),
                combination.getSecondClassification(),
                combination.getThirdClassification()
        );
        List<GoodsCommonBO> classifications = getClassificationsSafely(classificationIds);
        List<GoodsCommonBO> categories = getCategoriesSafely(
                combination.getCategoryId() != null ? Collections.singletonList(combination.getCategoryId()) :
                        Collections.emptyList());
        List<GoodsCommonBO> channels = getChannelsSafely(
                combination.getChannelId() != null ? Collections.singletonList(combination.getChannelId()) :
                        Collections.emptyList());

        // 更新人、创建人信息
        Long creatorId = combination.getCreatorId();
        Long updaterId = combination.getUpdaterId();
        List<UserBO> users =
                userService.getUserPage(UserPageQuery.builder().ids(Arrays.asList(creatorId, updaterId)).build());

        CombinationDetailVO result = CombinationVOConverter.toDetailVO(combination, skus, categories, channels,
                series, classifications);
        if (ObjectUtil.isNotNull(creatorId)) {
            result.setCreatorName(users.stream().filter(user -> user.getId().equals(creatorId)).findFirst().map(UserBO::getUsername).orElse(null));
        }
        if (ObjectUtil.isNotNull(updaterId)) {
            result.setUpdaterName(users.stream().filter(user -> user.getId().equals(updaterId)).findFirst().map(UserBO::getUsername).orElse(null));
        }
        log.debug("获取组合商品详情完成，ID：{}", id);
        return result;
    }

    /**
     * 创建组合商品
     *
     * @param request 创建请求参数，不能为空
     * @return 创建成功的组合商品ID
     * @throws IllegalArgumentException 当请求参数为空时抛出
     */
    public void create(@NotNull CombinationCreateRequest request) {
        log.info("开始创建组合商品，名称：{}", request.getCombinationName());

        // 获取当前操作人ID
        JwtUserDetails user = SecurityContextUtil.getCurrentUserDetails().get();
        // 参数校验
        validateCreateRequest(request);

        // 校验并获取主品信息
        CombinationSkuItem mainSku = request.getSkuIds().stream()
                .filter(CombinationSkuItem::getMain)
                .findFirst()
                .orElseThrow(() -> new BusinessException("主品不能为空"));

        // 查询主品SKU详细信息
        SkuDetailBO sku = skuService.getById(mainSku.getSkuId());
        if (ObjectUtil.isNull(sku)) {
            throw new BusinessException("主品SKU不存在，ID：" + mainSku.getSkuId());
        }

        // 校验所有SKU是否存在
        List<SkuListBO> skuInfos = validateAllSkusExist(request.getSkuIds());

        // 查询SPU信息（使用主品的SPU ID）
        SpuBO spuInfo = null;
        if (sku.getSpuId() != null) {
            // 根据SPU ID查询SPU信息
            spuInfo = spuService.getById(sku.getSpuId());
            if (ObjectUtil.isNull(spuInfo)) {
                throw new BusinessException("主品SPU不存在，ID：" + sku.getSpuId());
            }
        }
        // 转换为服务层命令对象，主品的SPU信息作为组合品的属性信息进行赋值
        CombinationCreateCommand command = CombinationVOConverter.toCreateCommand(request, user.getUserId(), sku,
                spuInfo);
        String combinationCode = CodeGenerationUtil.generateComboCode();
        command.setCombinationCode(combinationCode);
        //事务开始
        transactionTemplate.execute((TransactionCallback<Long>) status -> {
            // 调用服务层创建组合商品
            Long combinationId = combinationService.create(command);
            //创建泛微流程
            String requestId = createComboApprovalWorkflow(
                    command, user, skuInfos
            );
            //更新 oa 状态
            combinationService.updateOaStatus(
                    CombinationUpdateCommand.builder()
                            .id(combinationId)
                            .oaId(Long.valueOf(requestId))
                            .status(OaStatusEnum.APPROVING)
                            .build());
            return null;
        });
    }

    /**
     * 更新组合商品
     *
     * @param request 更新请求参数，不能为空
     * @throws IllegalArgumentException 当请求参数为空时抛出
     */
    public void update(@NotNull CombinationUpdateRequest request) {
        Objects.requireNonNull(request, "更新组合商品请求参数不能为空");
        Objects.requireNonNull(request.getId(), "组合商品ID不能为空");

        log.info("开始更新组合商品，ID：{}，名称：{}", request.getId(), request.getCombinationName());

        // 获取当前操作人ID
        Long operatorId = SecurityContextUtil.getCurrentUserIdLenient();

        // 检查组合商品是否存在
        CombinationDetailBO existingCombination = combinationService.getById(request.getId());
        if (existingCombination == null) {
            throw new IllegalArgumentException("组合商品不存在，ID：" + request.getId());
        }

        // 参数校验
        validateUpdateRequest(request);

        // 转换为服务层命令对象
        CombinationUpdateCommand command = CombinationVOConverter.toUpdateCommand(request, operatorId);

        // 调用服务层更新组合商品
        combinationService.updateRelationInfo(command);

        log.info("更新组合商品完成，ID：{}，名称：{}", request.getId(), request.getCombinationName());
    }

    /**
     * 检查组合商品名称是否已存在
     *
     * @param name 商品名称
     * @param excludeId 排除的ID（用于更新时排除自身）
     * @return 是否存在
     */
    public Boolean existsByName(String name, Long excludeId) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        return combinationService.existsByName(name.trim(), excludeId);
    }

    /**
     * 安全获取渠道信息
     */
    private List<GoodsCommonBO> getChannelsSafely(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        try {
            List<GoodsCommonBO> result = goodsCommonService.getChannels(ids);
            return result != null ? result : Collections.emptyList();
        } catch (Exception e) {
            log.warn("获取渠道信息失败，ids：{}", ids, e);
            return Collections.emptyList();
        }
    }

    /**
     * 安全获取系列信息
     */
    private List<GoodsCommonBO> getSeriesSafely(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        try {
            List<GoodsCommonBO> result = goodsCommonService.getSeries(ids);
            return result != null ? result : Collections.emptyList();
        } catch (Exception e) {
            log.warn("获取系列信息失败，ids：{}", ids, e);
            return Collections.emptyList();
        }
    }

    /**
     * 安全获取分类信息
     */
    private List<GoodsCommonBO> getCategoriesSafely(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        try {
            List<GoodsCommonBO> result = goodsCommonService.getCategory(ids);
            return result != null ? result : Collections.emptyList();
        } catch (Exception e) {
            log.warn("获取分类信息失败，ids：{}", ids, e);
            return Collections.emptyList();
        }
    }

    /**
     * 安全获取分类信息
     */
    private List<GoodsCommonBO> getClassificationsSafely(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        try {
            List<GoodsCommonBO> result = goodsCommonService.getClassification(ids);
            return result != null ? result : Collections.emptyList();
        } catch (Exception e) {
            log.warn("获取分类信息失败，ids：{}", ids, e);
            return Collections.emptyList();
        }
    }

    /**
     * 安全获取SKU信息
     */
    private List<SkuListBO> getSkusSafely(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        try {
            List<SkuListBO> result = skuService.list(SkuBOListQuery.builder().ids(ids).build());
            return result != null ? result : Collections.emptyList();
        } catch (Exception e) {
            log.warn("获取SKU信息失败，ids：{}", ids, e);
            return Collections.emptyList();
        }
    }

    /**
     * 收集组合商品列表中的所有关联ID
     *
     * @param combinations 组合商品列表
     * @return 关联ID集合
     */
    private CombinationRelatedIds collectRelatedIds(List<CombinationListBO> combinations) {
        if (CollectionUtils.isEmpty(combinations)) {
            return new CombinationRelatedIds();
        }

        CombinationRelatedIds relatedIds = new CombinationRelatedIds();
        Set<Long> skuIds = new HashSet<>();
        Set<Long> categoryIds = new HashSet<>();
        Set<Long> channelIds = new HashSet<>();
        Set<Long> seriesIds = new HashSet<>();
        Set<Long> classificationIds = new HashSet<>();

        for (CombinationListBO combination : combinations) {
            // 收集SKU ID
            if (CollUtil.isNotEmpty(combination.getSkuIds())) {
                skuIds.addAll(combination.getSkuIds());
            }

            // 收集其他ID
            addIfNotNull(categoryIds, combination.getCategoryId());
            addIfNotNull(channelIds, combination.getChannelId());

            // 收集系列ID
            if (CollUtil.isNotEmpty(combination.getSeriesIds())) {
                seriesIds.addAll(combination.getSeriesIds());
            }

            // 收集分类ID
            addIfNotNull(classificationIds, combination.getFirstClassification());
            addIfNotNull(classificationIds, combination.getSecondClassification());
            addIfNotNull(classificationIds, combination.getThirdClassification());
        }

        relatedIds.setSkuIds(new ArrayList<>(skuIds));
        relatedIds.setCategoryIds(new ArrayList<>(categoryIds));
        relatedIds.setChannelIds(new ArrayList<>(channelIds));
        relatedIds.setSeriesIds(new ArrayList<>(seriesIds));
        relatedIds.setClassificationIds(new ArrayList<>(classificationIds));

        return relatedIds;
    }

    /**
     * 安全构建分类ID列表
     */
    private List<Long> buildClassificationIds(Long first, Long second, Long third) {
        return Stream.of(first, second, third)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 安全添加非空元素到集合
     */
    private void addIfNotNull(Set<Long> set, Long value) {
        if (value != null) {
            set.add(value);
        }
    }

    /**
     * 校验更新组合商品请求参数
     *
     * @param request 更新请求
     */
    private void validateUpdateRequest(CombinationUpdateRequest request) {
        // 校验名称
        if (request.getCombinationName() != null && !request.getCombinationName().trim().isEmpty()) {
            // 检查名称是否重复（排除当前记录）
            if (existsByName(request.getCombinationName().trim(), request.getId())) {
                throw new IllegalArgumentException("组合商品名称已存在：" + request.getCombinationName().trim());
            }
        }

        // 校验SKU列表
        if (CollUtil.isNotEmpty(request.getSkuItems())) {
            // 校验是否只有一个主品
            long mainCount = request.getSkuItems().stream()
                    .filter(CombinationSkuItem::getMain)
                    .count();
            if (mainCount > 1) {
                throw new IllegalArgumentException("组合商品只能有一个主品");
            }

            // 校验SKU ID不能为空
            for (CombinationSkuItem item : request.getSkuItems()) {
                if (item.getSkuId() == null) {
                    throw new IllegalArgumentException("SKU ID不能为空");
                }
            }
        }
    }

    /**
     * 校验创建组合商品请求参数
     *
     * @param request 创建请求参数
     * @throws IllegalArgumentException 当参数不合法时抛出
     */
    private void validateCreateRequest(CombinationCreateRequest request) {
        // 校验组合商品名称
        if (request.getCombinationName() == null || request.getCombinationName().trim().isEmpty()) {
            throw new IllegalArgumentException("组合商品名称不能为空");
        }

        // 校验组合商品名称是否已存在
        if (existsByName(request.getCombinationName().trim(), null)) {
            throw new IllegalArgumentException("组合商品名称已存在：" + request.getCombinationName().trim());
        }

        // 校验SKU列表
        if (CollUtil.isEmpty(request.getSkuIds())) {
            throw new IllegalArgumentException("SKU列表不能为空");
        }

        // 校验是否只有一个主品
        long mainSkuCount = request.getSkuIds().stream()
                .filter(CombinationSkuItem::getMain)
                .count();
        if (mainSkuCount != 1) {
            throw new IllegalArgumentException("组合商品只能有一个主品");
        }
    }

    /**
     * 校验所有SKU是否存在
     *
     * @param skuItems SKU项列表
     * @throws IllegalArgumentException 当有SKU不存在时抛出
     */
    private List<SkuListBO> validateAllSkusExist(List<CombinationSkuItem> skuItems) {
        if (CollUtil.isEmpty(skuItems)) {
            Lists.newArrayList();
        }

        // 提取所有SKU ID
        List<Long> skuIds = skuItems.stream()
                .map(CombinationSkuItem::getSkuId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(skuIds)) {
            throw new BusinessException("SKU ID列表不能为空");
        }

        // 批量查询SKU信息
        List<SkuListBO> existingSkus = skuService.list(SkuBOListQuery.builder()
                .ids(skuIds)
                .oaStatuses(Arrays.asList(OaStatusEnum.APPROVED))
                .build());

        Set<Long> existingSkuIds = existingSkus.stream()
                .map(SkuListBO::getId)
                .collect(Collectors.toSet());

        // 检查是否有不存在的SKU
        List<Long> notExistSkuIds = skuIds.stream()
                .filter(id -> !existingSkuIds.contains(id))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(notExistSkuIds)) {
            throw new BusinessException("以下SKU不存在：" + notExistSkuIds);
        }

        log.debug("所有SKU校验通过，SKU数量：{}", skuIds.size());
        return existingSkus;
    }


    private String createComboApprovalWorkflow(CombinationCreateCommand command, JwtUserDetails user,
                                               List<SkuListBO> skuInfos) {
        Map<String, Object> params = CombinationVOConverter.convert2workflowRequest(command, user, skuInfos);
        String requestId = weaverWorkflowService.createWorkflowRequest(params, String.valueOf(user.getWeaverId()));
        if (StringUtils.isEmpty(requestId)) {
            throw new BusinessException("创建SKU立项审批工作流失败");
        } else {
            return requestId;
        }
    }
}
