package com.spes.sop.controller.goods.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 组合品批量导入结果响应VO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationBatchImportResultVO {

    /**
     * 总条数
     */
    private Integer totalCount;

    /**
     * 成功条数
     */
    private Integer successCount;

    /**
     * 失败条数
     */
    private Integer failCount;

    /**
     * 失败详情
     */
    private List<CombinationImportFailDetail> failDetails;

    /**
     * 组合品导入失败详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CombinationImportFailDetail {

        /**
         * 行号
         */
        private Integer rowNum;

        /**
         * 组合品名称
         */
        private String combinationName;

        /**
         * 失败原因
         */
        private String failReason;
    }
} 