package com.spes.sop.controller.goods.support;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Maps;
import com.spes.sop.common.enums.UserRoleEnum;
import com.spes.sop.common.exception.BusinessException;
import com.spes.sop.common.page.PageResult;
import com.spes.sop.common.security.SecurityContextUtil;
import com.spes.sop.common.util.CodeGenerationUtil;
import com.spes.sop.controller.goods.converter.SpuConverter;
import com.spes.sop.controller.goods.model.query.SpuPageQuery;
import com.spes.sop.controller.goods.model.request.SpuCreateRequest;
import com.spes.sop.controller.goods.model.request.SpuUpdateRequest;
import com.spes.sop.controller.goods.model.vo.SkuListVO;
import com.spes.sop.controller.goods.model.vo.SpuDetailVO;
import com.spes.sop.controller.goods.model.vo.SpuPageVO;
import com.spes.sop.goods.service.common.GoodsCommonService;
import com.spes.sop.goods.service.common.model.bo.GoodsCommonBO;
import com.spes.sop.goods.service.sku.SkuService;
import com.spes.sop.goods.service.sku.model.bo.SkuListBO;
import com.spes.sop.goods.service.sku.model.query.SkuBOListQuery;
import com.spes.sop.goods.service.spu.SpuService;
import com.spes.sop.goods.service.spu.model.bo.SpuBO;
import com.spes.sop.goods.service.spu.model.command.SpuCreateCommand;
import com.spes.sop.goods.service.spu.model.query.SpuBOListQuery;
import com.spes.sop.user.service.user.UserService;
import com.spes.sop.user.service.user.model.bo.UserBO;
import com.spes.sop.user.service.user.model.query.UserGetQuery;
import com.spes.sop.user.service.user.model.query.UserPageQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * SPU支持类 - 负责处理Controller层与Service层之间的数据转换和业务协调
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SpuSupport {

    private final SpuService spuService;
    private final SkuService skuService;
    private final UserService userService;
    private final GoodsCommonService goodsCommonService;
    private final TransactionTemplate transactionTemplate;


    /**
     * SPU分页查询
     *
     * @param request 分页查询请求
     * @return 分页查询结果
     */
    public PageResult<SpuPageVO> page(@NotNull SpuPageQuery request) {
        Objects.requireNonNull(request, "分页查询请求不能为空");
        log.info("开始执行SPU分页查询，查询参数：{}", request);

        // 转换查询参数
        SpuBOListQuery query = convertPageQueryToBusinessQuery(request);
        if (query == null) {
            log.warn("转换查询参数失败，返回空结果");
            return PageResult.of(Collections.emptyList(), 0L, request.getPageNum(), request.getPageSize());
        }

        // 先查询总数
        Long total = spuService.count(query);
        if (total == null || total <= 0) {
            log.info("查询结果为空，总数：{}", total);
            return PageResult.of(Collections.emptyList(), 0L, request.getPageNum(), request.getPageSize());
        }

        // 执行业务查询
        List<SpuBO> spuBOList = spuService.list(query);
        if (CollectionUtils.isEmpty(spuBOList)) {
            log.info("查询数据为空，但总数为：{}", total);
            return PageResult.of(Collections.emptyList(), total, request.getPageNum(), request.getPageSize());
        }

        //  更新人、创建人信息
        List<Long> userIds = spuBOList.stream().map(SpuBO::getCreatorId).collect(Collectors.toList());
        userIds.addAll(spuBOList.stream().map(SpuBO::getUpdaterId).collect(Collectors.toList()));
        List<UserBO> users = userService.getUserPage(UserPageQuery.builder().ids(userIds).build());
        // 转换为响应VO
        List<SpuPageVO> spuPageVOList = convertBOListToPageVOList(spuBOList, users);

        log.info("SPU分页查询完成，返回{}条记录，总数：{}", spuPageVOList.size(), total);
        return PageResult.of(spuPageVOList, total, request.getPageNum(), request.getPageSize());
    }

    /**
     * 根据ID查询SPU详情
     *
     * @param id SPU ID
     * @return SPU详情信息
     */
    public SpuDetailVO getById(Long id) {
        Objects.requireNonNull(id, "SPU ID不能为空");
        log.info("开始查询SPU详情，ID：{}", id);

        // 执行查询
        SpuBO spuBO = spuService.getById(id);
        if (spuBO == null) {
            log.warn("未找到SPU信息，ID：{}", id);
            return null;
        }

        // 转换为详情VO
        SpuDetailVO detailVO = convertBOToDetailVO(spuBO, SecurityContextUtil.getCurrentUserRoles());

        log.info("查询SPU详情完成，ID：{}，名称：{}", id, spuBO.getSpuName());
        return detailVO;
    }

    /**
     * 创建SPU
     *
     * @param request 创建请求
     */
    public void create(@Valid @NotNull SpuCreateRequest request) {
        Objects.requireNonNull(request, "创建请求不能为空");
        log.info("开始创建SPU，名称：{}", request.getSpuName());
        SpuBO spu = spuService.getBySpuName(request.getSpuName());
        if (ObjectUtil.isNotNull(spu)) {
            throw new BusinessException("该 SPU 名称已存在");
        }

        // 使用事务模板确保数据一致性
        transactionTemplate.execute(status -> {
            // 转换为业务对象
            SpuCreateCommand command = SpuConverter.convert2Command(request);
            if (command == null) {
                throw new IllegalArgumentException("转换创建请求失败");
            }

            // 生成SPU编码（优先使用三级分类，其次二级分类，最后一级分类）
            Long classificationId = command.getThirdClassification() != null ? command.getThirdClassification() :
                    (command.getSecondClassification() != null ? command.getSecondClassification() :
                            command.getFirstClassification());
            String spuCode = CodeGenerationUtil.generateSpuCode();
            command.setSpuCode(spuCode);

            // 从上下文获取当前用户ID
            Long currentUserId = SecurityContextUtil.getCurrentUserIdLenient();
            command.setCreatorId(currentUserId);
            command.setUpdaterId(currentUserId);

            // 执行创建逻辑
            spuService.create(command);

            log.info("创建SPU成功，名称：{}，编码：{}", request.getSpuName(), spuCode);
            return null;
        });
    }

    /**
     * 更新SPU
     *
     * @param request 更新请求
     */
    public void update(@Valid @NotNull SpuUpdateRequest request) {
        Objects.requireNonNull(request, "更新请求不能为空");
        Objects.requireNonNull(request.getId(), "SPU ID不能为空");
        log.info("开始更新SPU，ID：{}，名称：{}", request.getId(), request.getSpuName());

        // 使用事务模板确保数据一致性
        transactionTemplate.execute(status -> {
            // 检查SPU是否存在
            SpuDetailVO existingSpu = getById(request.getId());
            if (existingSpu == null) {
                throw new IllegalArgumentException("SPU不存在，ID：" + request.getId());
            }

            // 转换为业务对象
            SpuBO spuBO = SpuConverter.convertUpdateRequestToBO(request);
            if (spuBO == null) {
                throw new IllegalArgumentException("转换更新请求失败");
            }

            // 执行更新逻辑
            boolean success = spuService.update(spuBO);

            log.info("更新SPU成功，ID：{}，名称：{}", request.getId(), request.getSpuName());
            return null;
        });
    }

    /**
     * 转换分页查询请求为业务查询对象
     *
     * @param request 分页查询请求
     * @return 业务查询对象
     */
    private SpuBOListQuery convertPageQueryToBusinessQuery(SpuPageQuery request) {
        if (request == null) {
            return null;
        }

        SpuBOListQuery.SpuBOListQueryBuilder<?, ?> builder = SpuBOListQuery.builder()
                .spuCodes(Objects.nonNull(request.getSpuCode()) ?
                        Collections.singletonList(request.getSpuCode()) : null)
                .spuName(request.getSpuNameSearch())
                .firstClassifications(Objects.nonNull(request.getFirstClassification()) ?
                        Collections.singletonList(request.getFirstClassification()) : null)
                .secondClassifications(Objects.nonNull(request.getSecondClassification()) ?
                        Collections.singletonList(request.getSecondClassification()) : null)
                .thirdClassifications(Objects.nonNull(request.getThirdClassification()) ?
                        Collections.singletonList(request.getThirdClassification()) : null);

        // 设置分页信息
        int pageNum = request.getPageNum() != null ? request.getPageNum() : 1;
        int pageSize = request.getPageSize() != null ? request.getPageSize() : 10;
        builder.pager(new com.spes.sop.common.page.BasePager.Pager(pageNum, pageSize));

        return builder.build();
    }

    /**
     * 转换业务对象列表为分页VO列表
     *
     * @param spuBOList 业务对象列表
     * @return 分页VO列表
     */
    private List<SpuPageVO> convertBOListToPageVOList(List<SpuBO> spuBOList, List<UserBO> users) {
        if (CollectionUtils.isEmpty(spuBOList)) {
            return Collections.emptyList();
        }

        // 收集所有分类ID
        List<Long> classificationIds = spuBOList.stream()
                .flatMap(spu -> {
                    List<Long> ids = new java.util.ArrayList<>();
                    if (spu.getFirstClassification() != null) {
                        ids.add(spu.getFirstClassification());
                    }
                    if (spu.getSecondClassification() != null) {
                        ids.add(spu.getSecondClassification());
                    }
                    if (spu.getThirdClassification() != null) {
                        ids.add(spu.getThirdClassification());
                    }
                    return ids.stream();
                })
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询分类名称
        Map<Long, String> classificationNameMap = getClassificationNameMap(classificationIds);

        Map<Long, String> userMap = CollUtil.isNotEmpty(users) ? users.stream().collect(Collectors.toMap(UserBO::getId,
                UserBO::getUsername)) : Maps.newHashMap();

        // 转换为PageVO
        return spuBOList.stream()
                .map(spuBO -> convertBOToPageVO(spuBO, classificationNameMap, userMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 转换业务对象为分页VO
     *
     * @param spuBO                 SPU业务对象
     * @param classificationNameMap 分类名称映射
     * @return 分页VO
     */
    private SpuPageVO convertBOToPageVO(SpuBO spuBO, Map<Long, String> classificationNameMap,
                                        Map<Long, String> userMap) {
        if (spuBO == null) {
            return null;
        }

        // 获取用户姓名
        String creatorName = getUserName(spuBO.getCreatorId());
        String updaterName = getUserName(spuBO.getUpdaterId());

        // 获取分类名称
        String firstClassificationName = getClassificationName(spuBO.getFirstClassification(), classificationNameMap);
        String secondClassificationName = getClassificationName(spuBO.getSecondClassification(), classificationNameMap);
        String thirdClassificationName = getClassificationName(spuBO.getThirdClassification(), classificationNameMap);

        // 获取SKU编码列表
        List<String> skuCodes = getSkuCodesBySpu(spuBO);

        return SpuPageVO.builder()
                .id(spuBO.getId())
                .spuCode(spuBO.getSpuCode())
                .spuName(spuBO.getSpuName())
                .description(spuBO.getDescription())
                .firstClassification(firstClassificationName)
                .firstClassificationId(spuBO.getFirstClassification())
                .secondClassification(secondClassificationName)
                .secondClassificationId(spuBO.getSecondClassification())
                .thirdClassification(thirdClassificationName)
                .thirdClassificationId(spuBO.getThirdClassification())
                .skus(skuCodes)
                .creatorId(spuBO.getCreatorId())
                .creatorName(creatorName)
                .updaterId(spuBO.getUpdaterId())
                .updaterName(updaterName)
                .createTime(spuBO.getCreateTime())
                .updateTime(spuBO.getUpdateTime())
                .creatorName(userMap.getOrDefault(spuBO.getCreatorId(), ""))
                .updaterName(userMap.getOrDefault(spuBO.getUpdaterId(), ""))
                .build();
    }

    /**
     * 转换业务对象为详情VO
     *
     * @param spuBO 业务对象
     * @return 详情VO
     */
    private SpuDetailVO convertBOToDetailVO(SpuBO spuBO, String role) {
        if (spuBO == null) {
            return null;
        }

        // 获取SKU列表
        List<SkuListVO> skuItems = getSkuListBySpu(spuBO, role);

        // 获取创建人和更新人姓名
        String creatorName = getUserName(spuBO.getCreatorId());
        String updaterName = getUserName(spuBO.getUpdaterId());

        // 获取分类名称
        List<Long> classificationIds = new java.util.ArrayList<>();
        if (spuBO.getFirstClassification() != null) {
            classificationIds.add(spuBO.getFirstClassification());
        }
        if (spuBO.getSecondClassification() != null) {
            classificationIds.add(spuBO.getSecondClassification());
        }
        if (spuBO.getThirdClassification() != null) {
            classificationIds.add(spuBO.getThirdClassification());
        }

        Map<Long, String> classificationNameMap = getClassificationNameMap(classificationIds);
        String firstClassificationName = getClassificationName(spuBO.getFirstClassification(), classificationNameMap);
        String secondClassificationName = getClassificationName(spuBO.getSecondClassification(), classificationNameMap);
        String thirdClassificationName = getClassificationName(spuBO.getThirdClassification(), classificationNameMap);

        return SpuDetailVO.builder()
                .id(spuBO.getId())
                .spuCode(spuBO.getSpuCode())
                .spuName(spuBO.getSpuName())
                .description(spuBO.getDescription())
                .firstClassification(firstClassificationName)
                .firstClassificationId(spuBO.getFirstClassification())
                .secondClassification(secondClassificationName)
                .secondClassificationId(spuBO.getSecondClassification())
                .thirdClassification(thirdClassificationName)
                .thirdClassificationId(spuBO.getThirdClassification())
                .creatorId(spuBO.getCreatorId())
                .updaterId(spuBO.getUpdaterId())
                .createTime(spuBO.getCreateTime())
                .updateTime(spuBO.getUpdateTime())
                .skuItems(skuItems)
                .creatorName(creatorName)
                .updaterName(updaterName)
                .build();
    }

    /**
     * 根据SPU获取SKU列表
     *
     * @param spuBO SPU业务对象
     * @return SKU列表VO
     */
    private List<SkuListVO> getSkuListBySpu(SpuBO spuBO, String role) {
        if (spuBO == null || spuBO.getId() == null) {
            return Collections.emptyList();
        }

        // 构建查询条件
        SkuBOListQuery query = SkuBOListQuery.builder()
                .spuIds(Collections.singletonList(spuBO.getId()))
                .build();

        // 查询SKU列表
        List<SkuListBO> skuListBOs = skuService.list(query);
        if (CollectionUtils.isEmpty(skuListBOs)) {
            return Collections.emptyList();
        }

        // 转换为VO
        List<SkuListVO> result = skuListBOs.stream()
                .map(a -> convertSkuBOToVO(a, role))
                .collect(Collectors.toList());
        result.forEach(a -> {
            a.setSpuCode(spuBO.getSpuCode());
            a.setSpuName(spuBO.getSpuName());
        });

        return result;
    }

    /**
     * 转换SKU业务对象为VO
     *
     * @param skuListBO SKU业务对象
     * @return SKU列表VO
     */
    private SkuListVO convertSkuBOToVO(SkuListBO skuListBO, String role) {
        if (skuListBO == null) {
            return null;
        }

        return SkuListVO.builder()
                .id(skuListBO.getId())
                .skuCode(skuListBO.getSkuCode())
                .skuName(skuListBO.getSkuName())
                .barCode(skuListBO.getBarCode())
                .spec(skuListBO.getSpecification())
                .gift(skuListBO.getGift() != null && skuListBO.getGift() == 1)
                .card(skuListBO.getCard() != null && skuListBO.getCard() == 1)
                .status(ObjectUtil.isNull(skuListBO.getOaStatus()) ? null : skuListBO.getOaStatus().name())
                .statusDesc(ObjectUtil.isNull(skuListBO.getOaStatus()) ? null : skuListBO.getOaStatus().getDesc())
                .syncFail(skuListBO.getSyncFail())
                .fairPrice(skuListBO.getFairPrice())
                .basePrice(UserRoleEnum.getPricePermissionRole().contains(role) ?
                        skuListBO.getBasePrice() : null)
                .cost(skuListBO.getCost())
                .createTime(skuListBO.getCreateTime() != null ? skuListBO.getCreateTime() : null)
                .updateTime(skuListBO.getUpdateTime() != null ? skuListBO.getUpdateTime() : null)
                .creatorName(getUserName(skuListBO.getCreatorId()))
                .updaterName(getUserName(skuListBO.getUpdaterId()))
                .build();

    }

    /**
     * 根据用户ID获取用户姓名
     *
     * @param userId 用户ID
     * @return 用户姓名，如果未找到返回"未知"
     */
    private String getUserName(Long userId) {
        if (userId == null) {
            return "未知";
        }

        UserGetQuery query = UserGetQuery.builder()
                .id(userId)
                .build();

        UserBO userBO = userService.getUser(query);
        return userBO != null ? userBO.getUsername() : "未知用户";
    }

    /**
     * 批量获取分类名称映射
     *
     * @param classificationIds 分类ID列表
     * @return 分类ID到名称的映射
     */
    private Map<Long, String> getClassificationNameMap(List<Long> classificationIds) {
        if (CollectionUtils.isEmpty(classificationIds)) {
            return Collections.emptyMap();
        }

        List<GoodsCommonBO> classifications = goodsCommonService.getClassification(classificationIds);
        return classifications.stream()
                .collect(Collectors.toMap(
                        GoodsCommonBO::getId,
                        GoodsCommonBO::getName,
                        (existing, replacement) -> existing // 保留现有值，防止重复键
                ));
    }

    /**
     * 从映射中获取分类名称
     *
     * @param classificationId      分类ID
     * @param classificationNameMap 分类名称映射
     * @return 分类名称，未找到返回"未知分类"
     */
    private String getClassificationName(Long classificationId, Map<Long, String> classificationNameMap) {
        if (classificationId == null) {
            return null;
        }

        return classificationNameMap.getOrDefault(classificationId, "未知分类");
    }

    /**
     * 根据SPU获取SKU编码列表
     *
     * @param spuBO SPU业务对象
     * @return SKU编码列表
     */
    private List<String> getSkuCodesBySpu(SpuBO spuBO) {
        if (spuBO == null || spuBO.getId() == null) {
            return Collections.emptyList();
        }

        // 构建查询条件
        SkuBOListQuery query = SkuBOListQuery.builder()
                .spuIds(Collections.singletonList(spuBO.getId()))
                .build();

        // 查询SKU列表
        List<SkuListBO> skuListBOs = skuService.list(query);
        if (CollectionUtils.isEmpty(skuListBOs)) {
            return Collections.emptyList();
        }

        // 提取SKU编码
        return skuListBOs.stream()
                .map(SkuListBO::getSkuCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
