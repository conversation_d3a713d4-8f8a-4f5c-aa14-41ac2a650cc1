package com.spes.sop.support.goods;

import com.spes.sop.common.util.CodeGenerationUtil;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

/**
 * SKU批量导入性能测试
 *
 * <AUTHOR>
 */
@SpringBootTest
public class SkuBatchImportPerformanceTest {

    /**
     * 测试批量生成唯一编码的性能和唯一性
     */
    @Test
    public void testGenerateUniqueSkuCodes() {
        List<Long> specCodes = Arrays.asList(1L, 2L, 3L, 1L, 2L, 4L, 5L, 1L, 3L, 6L);

        long startTime = System.currentTimeMillis();
        List<String> skuCodes = CodeGenerationUtil.generateUniqueSkuCodes(specCodes);
        long endTime = System.currentTimeMillis();

        System.out.println("生成" + specCodes.size() + "个SKU编码耗时：" + (endTime - startTime) + "ms");

        // 打印生成的编码
        for (int i = 0; i < skuCodes.size(); i++) {
            System.out.println("规格" + specCodes.get(i) + " -> " + skuCodes.get(i));
        }

        // 验证唯一性
        long uniqueCount = skuCodes.stream().distinct().count();
        System.out.println("生成编码数量：" + skuCodes.size() + "，唯一编码数量：" + uniqueCount);

        assert skuCodes.size() == uniqueCount : "生成的编码不唯一！";
    }

    /**
     * 测试大批量生成编码的性能
     */
    @Test
    public void testLargeBatchCodeGeneration() {
        // 生成1000个相同规格的编码，测试唯一性
        List<Long> specCodes = new java.util.ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            specCodes.add(1L);
        }

        long startTime = System.currentTimeMillis();
        List<String> skuCodes = CodeGenerationUtil.generateUniqueSkuCodes(specCodes);
        long endTime = System.currentTimeMillis();

        System.out.println("生成" + specCodes.size() + "个SKU编码耗时：" + (endTime - startTime) + "ms");

        // 验证唯一性
        long uniqueCount = skuCodes.stream().distinct().count();
        System.out.println("生成编码数量：" + skuCodes.size() + "，唯一编码数量：" + uniqueCount);

        assert skuCodes.size() == uniqueCount : "生成的编码不唯一！";
    }
} 