/*
 * Copyright(C) 2024 Spes Co., Ltd. All rights reserved.
 */

package com.spes.sop.controller.goods;

import com.spes.sop.third.kingdee.config.KingdeeConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 金蝶配置验证测试
 * 验证 KingdeeConfig 是否正确读取 yml 配置文件
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("dev")
@DisplayName("金蝶配置验证测试")
class KingdeeConfigValidationTest {

    @Autowired
    private KingdeeConfig kingdeeConfig;

    @Test
    @DisplayName("验证配置属性是否正确读取")
    void testConfigurationPropertiesBinding() {
        log.info("开始验证金蝶配置属性是否正确读取...");

        // 验证配置对象不为空
        assertNotNull(kingdeeConfig, "金蝶配置对象应该不为空");

        // 验证从 yml 文件读取的配置
        log.info("服务器地址: {}", kingdeeConfig.getServerUrl());
        log.info("用户名: {}", kingdeeConfig.getUsername());
        log.info("账套ID: {}", kingdeeConfig.getAcctId());
        log.info("语言ID: {}", kingdeeConfig.getLcid());
        log.info("连接超时: {}ms", kingdeeConfig.getConnectTimeout());
        log.info("读取超时: {}ms", kingdeeConfig.getReadTimeout());
        log.info("Session过期时间: {}ms", kingdeeConfig.getSessionExpireTime());

        // 验证是否读取到了 yml 配置而不是默认值
        assertNotEquals("http://localhost/k3cloud/", kingdeeConfig.getServerUrl(),
                "应该读取到 yml 配置的服务器地址，而不是默认值");
        assertNotEquals("admin", kingdeeConfig.getUsername(),
                "应该读取到 yml 配置的用户名，而不是默认值");
        assertNotEquals("66d67292cd0454", kingdeeConfig.getAcctId(),
                "应该读取到 yml 配置的账套ID，而不是默认值");

        // 验证具体的配置值（来自 application-dev.yml）
        assertEquals("http://**************/k3cloud/", kingdeeConfig.getServerUrl(),
                "服务器地址应该匹配 dev 配置");
        assertEquals("administrator", kingdeeConfig.getUsername(),
                "用户名应该匹配 dev 配置");
        assertEquals("kingdee123@", kingdeeConfig.getPassword(),
                "密码应该匹配 dev 配置");
        assertEquals("66d66a2167314d", kingdeeConfig.getAcctId(),
                "账套ID应该匹配 dev 配置");
        assertEquals(Integer.valueOf(2052), kingdeeConfig.getLcid(),
                "语言ID应该匹配 dev 配置");
        assertEquals(Integer.valueOf(10000), kingdeeConfig.getConnectTimeout(),
                "连接超时应该匹配 dev 配置");
        assertEquals(Integer.valueOf(30000), kingdeeConfig.getReadTimeout(),
                "读取超时应该匹配 dev 配置");
        assertEquals(Long.valueOf(60000L), kingdeeConfig.getSessionExpireTime(),
                "Session过期时间应该匹配 dev 配置");

        // 验证多品牌账套配置
        assertNotNull(kingdeeConfig.getAccounts(), "多品牌账套配置不应该为空");
        assertFalse(kingdeeConfig.getAccounts().isEmpty(), "多品牌账套配置应该包含数据");

        // 验证特定品牌的账套ID
        assertEquals("66d66a2167314d", kingdeeConfig.getAcctIdByBrand("默认"),
                "默认品牌的账套ID应该正确");
        assertEquals("66d66a2167314d", kingdeeConfig.getAcctIdByBrand("品牌A"),
                "品牌A的账套ID应该正确");

        log.info("✅ 金蝶配置属性验证成功，所有配置均已正确读取！");
    }

    @Test
    @DisplayName("验证品牌账套映射功能")
    void testBrandAccountMapping() {
        log.info("开始验证品牌账套映射功能...");

        // 验证已配置的品牌
        String defaultAcctId = kingdeeConfig.getAcctIdByBrand("默认");
        String brandAAcctId = kingdeeConfig.getAcctIdByBrand("品牌A");
        String brandBAcctId = kingdeeConfig.getAcctIdByBrand("品牌B");

        assertEquals("66d66a2167314d", defaultAcctId, "默认品牌账套ID正确");
        assertEquals("66d66a2167314d", brandAAcctId, "品牌A账套ID正确");
        assertEquals("66d66a2167314d", brandBAcctId, "品牌B账套ID正确");

        // 验证未配置的品牌回退到默认账套
        String unknownBrandAcctId = kingdeeConfig.getAcctIdByBrand("未知品牌");
        assertEquals(kingdeeConfig.getAcctId(), unknownBrandAcctId,
                "未知品牌应该回退到默认账套ID");

        log.info("✅ 品牌账套映射功能验证成功！");
    }
} 