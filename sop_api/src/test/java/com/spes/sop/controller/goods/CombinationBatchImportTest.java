package com.spes.sop.controller.goods;

import com.alibaba.excel.EasyExcel;
import com.spes.sop.controller.goods.model.request.CombinationBatchImportRequest;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 组合品批量导入测试
 *
 * <AUTHOR>
 */
@SpringBootTest
public class CombinationBatchImportTest {

    /**
     * 生成测试用的Excel文件
     */
    @Test
    public void generateTestExcel() {
        String fileName = "combination_import_test.xlsx";

        // 创建测试数据
        List<CombinationBatchImportRequest> testData = new ArrayList<>();

        // 组合品A - 包含主品和赠品
        testData.add(CombinationBatchImportRequest.builder()
                .combinationName("测试组合品A")
                .description("测试组合品A的描述")
                .skuCode("SKU001")
                .skuName("测试SKU001名称")
                .skuNum(1)
                .mainStr("是")
                .giftStr("否")
                .build());

        testData.add(CombinationBatchImportRequest.builder()
                .combinationName("测试组合品A")
                .description("测试组合品A的描述")
                .skuCode("SKU002")
                .skuName("测试SKU002名称")
                .skuNum(2)
                .mainStr("否")
                .giftStr("是")
                .build());

        // 组合品B - 只有主品
        testData.add(CombinationBatchImportRequest.builder()
                .combinationName("测试组合品B")
                .description("测试组合品B的描述")
                .skuCode("SKU003")
                .skuName("测试SKU003名称")
                .skuNum(1)
                .mainStr("是")
                .giftStr("否")
                .build());

        // 组合品C - 多个SKU
        testData.add(CombinationBatchImportRequest.builder()
                .combinationName("测试组合品C")
                .description("测试组合品C的描述")
                .skuCode("SKU004")
                .skuName("测试SKU004名称")
                .skuNum(1)
                .mainStr("是")
                .giftStr("否")
                .build());

        testData.add(CombinationBatchImportRequest.builder()
                .combinationName("测试组合品C")
                .description("测试组合品C的描述")
                .skuCode("SKU005")
                .skuName("测试SKU005名称")
                .skuNum(3)
                .mainStr("否")
                .giftStr("否")
                .build());

        testData.add(CombinationBatchImportRequest.builder()
                .combinationName("测试组合品C")
                .description("测试组合品C的描述")
                .skuCode("SKU006")
                .skuName("测试SKU006名称")
                .skuNum(1)
                .mainStr("否")
                .giftStr("是")
                .build());

        // 写入Excel文件
        EasyExcel.write(fileName, CombinationBatchImportRequest.class)
                .sheet("组合品导入数据")
                .doWrite(testData);

        System.out.println("测试Excel文件已生成：" + new File(fileName).getAbsolutePath());
    }
} 