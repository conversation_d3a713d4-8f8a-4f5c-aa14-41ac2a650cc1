package com.spes.sop.controller.goods;

import com.alibaba.excel.EasyExcel;
import com.spes.sop.controller.goods.model.request.SkuBatchImportRequest;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * SKU批量导入测试
 *
 * <AUTHOR>
 */
@SpringBootTest
public class SkuBatchImportTest {

    /**
     * 生成测试用的Excel文件
     */
    @Test
    public void generateTestExcel() {
        String fileName = "sku_import_test.xlsx";

        // 创建测试数据
        List<SkuBatchImportRequest> testData = new ArrayList<>();

        // 添加测试数据
        testData.add(SkuBatchImportRequest.builder()
                .skuName("测试SKU-001")
                .specification(1L)
                .applicationDate(new Date())
                .launchDate(new Date())
                .channelExclusiveStr("否")
                .productPurpose("测试用途")
                .estimatedPrice(new BigDecimal("199.99"))
                .firstBatchQuantity(1000)
                .factoryMinOrderQuantity(500)
                .minOrderQuantity(100)
                .minOrderReason("测试起订原因")
                .shelfLifeRequirement("24个月")
                .exclusiveSaleStr("否")
                .salesResponsibleEmployeeId("EMP001")
                .remark("测试备注")
                .build());

        testData.add(SkuBatchImportRequest.builder()
                .skuName("测试SKU-002")
                .specification(2L)
                .applicationDate(new Date())
                .launchDate(new Date())
                .channelExclusiveStr("是")
                .productPurpose("特供产品")
                .estimatedPrice(new BigDecimal("299.99"))
                .firstBatchQuantity(2000)
                .factoryMinOrderQuantity(1000)
                .minOrderQuantity(200)
                .minOrderReason("特供起订原因")
                .shelfLifeRequirement("18个月")
                .exclusiveSaleStr("是")
                .salesResponsibleEmployeeId("EMP002")
                .remark("特供备注")
                .build());

        // 写入Excel文件
        EasyExcel.write(fileName, SkuBatchImportRequest.class)
                .sheet("SKU导入数据")
                .doWrite(testData);

        System.out.println("测试Excel文件已生成：" + new File(fileName).getAbsolutePath());
    }
} 